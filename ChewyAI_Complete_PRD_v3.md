# ChewyAI - Agentic Development Specification
## Complete Implementation Guide for AI-Powered Study Platform

> **Purpose**: This specification is designed for agentic coding assistants to build the complete ChewyAI platform in a single development session. Every technical detail, file structure, and implementation requirement is precisely defined for autonomous development.

## Quick Start Context Primer
**Platform**: AI-powered study material generation SaaS
**Tech Stack**: React + TypeScript + TailwindCSS frontend, Express + TypeScript backend, Supabase database + auth, Stripe payments, OpenRouter AI
**Architecture**: Monorepo with frontend building into backend's public folder for single-server deployment
**Core Features**: Document upload → AI flashcard/quiz generation → Interactive study interfaces
**Business Model**: Credit-based AI generation with subscription tiers



## Table of Contents



1.  [Executive Summary](#1-executive-summary)

2.  [System Architecture](#2-system-architecture)

3.  [Business Model & Pricing](#3-business-model--pricing)

4.  [Database Design](#4-database-design)

5.  [API Specification](#5-api-specification)

6.  [Frontend Technical Specification](#6-frontend-technical-specification)

7.  [Backend Technical Specification](#7-backend-technical-specification)

8.  [Core Features & User Flows](#8-core-features--user-flows)

9.  [UI/UX Design System](#9-uiux-design-system)

10. [Development Setup Guide](#10-development-setup-guide)

11. [Deployment Guide](#11-deployment-guide)

12. [Security Implementation](#12-security-implementation)

13. [Third-Party Integration Guide](#13-third-party-integration-guide)

14. [Scalability and Performance](#14-scalability-and-performance)



---



## 1. Executive Summary



ChewyAI is an AI-powered study material generation SaaS platform that allows users to upload documents (PDF, DOCX, TXT, PPTX) and generate flashcards and quizzes using AI. The platform features a subscription-based credit system, embedded document viewing, and interactive study interfaces.



### Key Features

- Document upload and embedded viewing with direct AI generation buttons

- Multi-document selection for AI content generation

- Four quiz question types: multiple choice, short answer, select all that apply, true/false

- Flashcard interface with keyboard navigation and flagging

- Credit-based AI generation system with flexible pricing

- Dark space and purple theme with Material UI design

- Manual content creation (free) vs AI generation (paid)



### Technology Stack

- **Frontend:** ReactJS with TypeScript, TailwindCSS, react-icons

- **Backend:** ExpressJS with TypeScript, bcrypt for password hashing

- **Database & Auth:** Supabase (PostgreSQL + Authentication)

- **Payments:** Stripe API for subscriptions and credit purchases

- **AI:** OpenRouter LLM API (Gemini-2.5-Pro)

- **Deployment:** Monorepo structure, ExpressJS serving React build in production



---



## 2. System Architecture



### Project Structure

```

chewy-ai/

├── frontend/                     # React + TypeScript

│   ├── src/

│   ├── package.json

│   ├── tsconfig.json

│   ├── tailwind.config.js

│   └── vite.config.ts

├── backend/                      # Express + TypeScript

│   ├── src/

│   ├── public/                  # Frontend build output

│   ├── package.json

│   ├── tsconfig.json

│   └── .env

├── shared/

│   └── types.ts                 # Shared TypeScript types

├── package.json                 # Root package.json

└── README.md

```



### System Components



```mermaid

graph TB

    A[User] --> B[ReactJS Frontend]

    B --> C[ExpressJS Backend API]

    C --> D[Supabase Database + Storage]

    C --> E[OpenRouter AI API]

    C --> F[Stripe API]

    

    G[Production] --> H[Single ExpressJS Server]

    H --> I[Serves API Routes /api/*]

    H --> J[Serves React Build Static Files /*]

    

    subgraph "Secure Backend-Only Integrations"

        K[Supabase Client]

        L[Stripe Client]

        M[OpenRouter Client]

        N[File Processing]

    end

    

    C --> K

    C --> L

    C --> M

    C --> N

    

    subgraph "Frontend Security"

        O[Frontend only calls Backend API]

        P[No direct external service access]

        Q[All credentials server-side only]

    end

    

    B --> O

```



### Data Flow

1. User uploads documents through React frontend

2. Backend processes and stores documents with extracted text

3. User views documents in embedded viewer with AI generation buttons

4. User selects documents and triggers AI generation

5. Backend checks credits, combines document content, calls OpenRouter API

6. AI-generated content stored in database, credits deducted

7. User studies with flashcard/quiz interfaces



---



## 2.5. Shared Type Definitions



This section provides the complete TypeScript type definitions that serve as the single source of truth for all data models shared between the frontend and backend. These types eliminate ambiguity and ensure consistency across the entire application.



### Core Type Definitions File: `shared/types.ts`



```typescript

/**

 * =================================================================

 * SHARED TYPES - CHEWYAI

 * =================================================================

 * This file is the single source of truth for all data models

 * shared between the frontend and backend.

 */



// ----------------------------------------

// User & Auth

// ----------------------------------------

export interface UserProfile {

  id: string; // Corresponds to Supabase auth.uid()

  email: string;

  name?: string;

  subscription_tier: 'Free' | 'Basic' | 'Pro';

  credits_remaining: number;

  subscription_expires_at?: string; // ISO 8601 date string

  stripe_customer_id?: string;

  last_login?: string; // ISO 8601 date string

  is_active: boolean;

  created_at: string; // ISO 8601 date string

}



export interface AuthResult {

  success: boolean;

  user?: UserProfile;

  token?: string;

  error?: string;

}



// ----------------------------------------

// Documents

// ----------------------------------------

export type DocumentFileType = 'pdf' | 'docx' | 'txt' | 'pptx';



export interface DocumentMetadata {

  id: string;

  user_id: string;

  filename: string;

  file_type: DocumentFileType;

  file_size: number; // in bytes

  supabase_storage_path: string;

  uploaded_at: string; // ISO 8601 date string

  is_processed: boolean;

  processing_error?: string;

}



export interface DocumentWithContent extends DocumentMetadata {

  content_text: string;

}



// ----------------------------------------

// Study Sets & Content

// ----------------------------------------

export type StudySetType = 'flashcards' | 'quiz';



export interface StudySet {

  id: string;

  user_id: string;

  name: string;

  type: StudySetType;

  is_ai_generated: boolean;

  source_documents?: { id: string; filename: string }[]; // Array of document IDs and names

  custom_prompt?: string;

  total_items: number;

  last_studied_at?: string; // ISO 8601 date string

  created_at: string; // ISO 8601 date string

  updated_at: string; // ISO 8601 date string

}



export interface Flashcard {

  id: string;

  study_set_id: string;

  front: string;

  back: string;

  is_flagged: boolean;

  is_ai_generated: boolean;

  difficulty_level?: number; // e.g., 1-5

  times_reviewed: number;

  last_reviewed_at?: string; // ISO 8601 date string

}



export type QuestionType = 'multiple_choice' | 'select_all' | 'true_false' | 'short_answer';



export interface QuizQuestion {

  id: string;

  study_set_id: string;

  question_text: string;

  question_type: QuestionType;

  options?: string[]; // For multiple_choice, select_all

  correct_answers: string[]; // Can be ['True'] or ['False'] for true_false

  explanation?: string;

  is_ai_generated: boolean;

  difficulty_level?: number;

  times_attempted: number;

  times_correct: number;

}



// ----------------------------------------

// Credits & Pricing

// ----------------------------------------

export interface CreditTransaction {

  id: string;

  user_id: string;

  credits_used: number; // Negative for additions

  operation_type: string;

  description: string;

  metadata?: Record<string, any>;

  study_set_id?: string;

  created_at: string; // ISO 8601 date string

}



export interface AIOperationCost {

  operation_type: string;

  credits_required: number;

  is_active: boolean;

}



// ----------------------------------------

// API Response Types

// ----------------------------------------

export interface APIResponse<T = any> {

  success: boolean;

  data?: T;

  error?: string;

  message?: string;

}



export interface PaginatedResponse<T> extends APIResponse<T[]> {

  total: number;

  page: number;

  limit: number;

  hasMore: boolean;

}



// ----------------------------------------

// Frontend Component Props

// ----------------------------------------

export interface BaseComponentProps {

  className?: string;

  'data-testid'?: string;

}



// Button component props

export interface ButtonProps extends BaseComponentProps {

  children: React.ReactNode;

  onClick?: () => void;

  variant?: 'primary' | 'secondary' | 'danger';

  size?: 'sm' | 'md' | 'lg';

  isLoading?: boolean;

  disabled?: boolean;

  type?: 'button' | 'submit' | 'reset';

}



// Input component props

export interface InputProps extends BaseComponentProps {

  label?: string;

  placeholder?: string;

  value: string;

  onChange: (value: string) => void;

  type?: 'text' | 'email' | 'password' | 'number';

  error?: string;

  required?: boolean;

  disabled?: boolean;

}



// Modal component props

export interface ModalProps extends BaseComponentProps {

  isOpen: boolean;

  onClose: () => void;

  title?: string;

  children: React.ReactNode;

  size?: 'sm' | 'md' | 'lg' | 'xl';

}

```



### Type Usage Guidelines



1. **Import Pattern**: All components and services must import types from `shared/types.ts`

2. **Consistency Rule**: Never redefine these types elsewhere in the codebase

3. **Extension Pattern**: Use interface extension for component-specific props that build on base types

4. **Validation**: Backend controllers must validate incoming data against these types



---



## 3. Business Model & Pricing



### Credit System

- **1 credit = 1 AI generation request** (regardless of output size)

- **Cost basis:** $0.04 per OpenRouter request with 400% markup

- **Flexible pricing:** Configurable credit costs per feature type through database



### Subscription Tiers

| Tier | Price | Credits | Study Sets | Quizzes | Additional Features |

|------|-------|---------|------------|---------|-------------------|

| Free | $0 | 10/month | Max 3 | Max 3 | Manual creation unlimited |

| Basic | $9.99/month | 100 | Unlimited | Unlimited | Priority support |

| Pro | $19.99/month | 500 | Unlimited | Unlimited | Advanced analytics |



### Credit Packages (À la carte)

- 25 credits: $4.99 ($0.20/credit)

- 50 credits: $8.99 ($0.18/credit)

- 100 credits: $15.99 ($0.16/credit)



### Credit Configuration Table

```sql

-- Enables dynamic pricing changes

CREATE TABLE ai_operation_costs (

    id UUID PRIMARY KEY,

    operation_type VARCHAR(50) NOT NULL, -- 'flashcard_generation', 'quiz_generation'

    credits_required INTEGER NOT NULL,

    created_at TIMESTAMP DEFAULT NOW(),

    is_active BOOLEAN DEFAULT TRUE

);

```



---



## 4. Database Design



### Database Security Architecture



```mermaid

flowchart TD

    A[User Request] --> B[Backend API]

    B --> C[Supabase Client with Service Key]

    C --> D{RLS Policy Check}

    D -->|Pass| E[Database Operation]

    D -->|Fail| F[Access Denied]



    subgraph "Row Level Security"

        G[User Data Isolation]

        H[Resource Ownership]

        I[Operation Restrictions]

    end



    subgraph "Stored Procedures"

        J[Credit Deduction]

        K[Subscription Updates]

        L[Audit Logging]

    end



    D --> G

    D --> H

    D --> I

    E --> J

    E --> K

    E --> L

```



### Enhanced Schema with Security (Supabase/PostgreSQL)



```mermaid

erDiagram

    users {

        uuid id PK

        string email

        string name

        string password_hash

        timestamp created_at

        timestamp updated_at

        string subscription_tier

        integer credits_remaining

        timestamp subscription_expires_at

        string stripe_customer_id

        timestamp last_login

        boolean is_active

    }



    documents {

        uuid id PK

        uuid user_id FK

        string filename

        string file_type

        integer file_size

        text content_text

        string supabase_storage_path

        timestamp uploaded_at

        boolean is_processed

        text processing_error

    }



    study_sets {

        uuid id PK

        uuid user_id FK

        string name

        string type

        timestamp created_at

        timestamp updated_at

        boolean is_ai_generated

        jsonb source_documents

        text custom_prompt

        integer total_items

        timestamp last_studied_at

    }



    flashcards {

        uuid id PK

        uuid study_set_id FK

        text front

        text back

        boolean is_flagged

        timestamp created_at

        boolean is_ai_generated

        integer difficulty_level

        integer times_reviewed

        timestamp last_reviewed_at

    }



    quiz_questions {

        uuid id PK

        uuid study_set_id FK

        text question_text

        string question_type

        jsonb options

        jsonb correct_answers

        text explanation

        timestamp created_at

        boolean is_ai_generated

        integer difficulty_level

        integer times_attempted

        integer times_correct

    }



    credit_transactions {

        uuid id PK

        uuid user_id FK

        integer credits_used

        string operation_type

        string description

        timestamp created_at

        jsonb metadata

        uuid study_set_id FK

        inet ip_address

        text user_agent

    }



    ai_operation_costs {

        uuid id PK

        string operation_type

        integer credits_required

        timestamp created_at

        timestamp updated_at

        boolean is_active

        text description

    }



    users ||--o{ documents : uploads

    users ||--o{ study_sets : creates

    users ||--o{ credit_transactions : has

    study_sets ||--o{ flashcards : contains

    study_sets ||--o{ quiz_questions : contains

    credit_transactions }o--|| study_sets : references

```



### Key Database Security Features



#### Row Level Security (RLS) Policies

- **Complete data isolation** between users

- **Inheritance security** for related tables (flashcards inherit study_set ownership)

- **Append-only audit tables** prevent tampering with credit history

- **Admin-only configuration** for AI operation costs



#### Critical Stored Procedures

- **Atomic credit deduction** prevents race conditions

- **Subscription management** with automatic credit allocation

- **Audit logging** for all sensitive operations

- **User statistics** for dashboard analytics



#### Data Integrity Features

- **Check constraints** prevent invalid data (file sizes, credit amounts)

- **Foreign key cascades** maintain referential integrity

- **Automatic triggers** for item count updates

- **Performance indexes** on frequently queried fields



---



## 5. API Specification



### Authentication (Supabase)

- `POST /api/auth/signup` - Register new user

- `POST /api/auth/login` - Authenticate user

- `POST /api/auth/logout` - Logout user

- `GET /api/auth/user` - Get current user profile



### Document Management

- `POST /api/documents/upload` - Upload document with file processing

- `GET /api/documents` - Get user's documents with metadata

- `GET /api/documents/:id` - Get specific document with content

- `GET /api/documents/:id/view` - Serve document for embedded viewer

- `DELETE /api/documents/:id` - Delete document



### Study Sets

- `POST /api/study-sets` - Create study set (manual or AI)

- `GET /api/study-sets` - Get user's study sets

- `GET /api/study-sets/:id` - Get study set with content

- `PUT /api/study-sets/:id` - Update study set

- `DELETE /api/study-sets/:id` - Delete study set



### AI Generation

- `POST /api/ai/generate-flashcards`

  ```json

  {

    "document_ids": ["uuid1", "uuid2"],

    "custom_prompt": "Focus on key concepts",

    "count": 10

  }

  ```

- `POST /api/ai/generate-quiz`

  ```json

  {

    "document_ids": ["uuid1", "uuid2"],

    "question_types": ["multiple_choice", "true_false"],

    "custom_prompt": "Focus on practical applications",

    "count": 5

  }

  ```

- `POST /api/ai/generate-more/:study_set_id` - Add more AI content to existing set



### Credit Management

- `GET /api/credits/balance` - Get current credit balance

- `GET /api/credits/history` - Get credit transaction history

- `GET /api/credits/pricing` - Get current AI operation costs



### Subscription & Payments

- `POST /api/stripe/create-subscription` - Create Stripe subscription

- `POST /api/stripe/purchase-credits` - Purchase credit package

- `POST /api/stripe/webhook` - Handle Stripe webhooks

- `GET /api/subscription/status` - Get subscription status



---



## 6. Frontend Technical Specification



### Technology Stack

- **React 18** with **TypeScript**

- **TailwindCSS** for styling with dark space/purple theme

- **react-icons** for consistent iconography

- **React Router** for navigation

- **Zustand** for global state management

- **React Query** for server state and caching

- **Framer Motion** for hover animations and transitions



### Component Architecture

```

src/

├── components/

│   ├── common/

│   │   ├── Button.tsx              # Material UI style with hover effects

│   │   ├── Input.tsx               # Form inputs with validation

│   │   ├── Modal.tsx               # Overlay modals

│   │   ├── Loading.tsx             # Loading spinners

│   │   └── CreditBalance.tsx       # Credit display component

│   ├── documents/

│   │   ├── DocumentUpload.tsx      # Drag & drop upload

│   │   ├── DocumentList.tsx        # Document library view

│   │   ├── DocumentViewer.tsx      # Embedded document viewer

│   │   ├── DocumentSelector.tsx    # Multi-select with checkboxes

│   │   └── AIGenerationPanel.tsx   # AI generation controls

│   ├── study/

│   │   ├── FlashcardViewer.tsx     # Keyboard navigation + flagging

│   │   ├── QuizInterface.tsx       # Multi-question-type support

│   │   ├── StudySetEditor.tsx      # Manual editing + AI augmentation

│   │   └── StudySetList.tsx        # Dashboard overview

│   ├── subscription/

│   │   ├── PricingCard.tsx         # Subscription tiers

│   │   ├── CreditPurchase.tsx      # Credit package selection

│   │   └── SubscriptionManager.tsx # Billing management

│   └── layout/

│       ├── Header.tsx              # Navigation with credit balance

│       ├── Sidebar.tsx             # App navigation

│       └── Layout.tsx              # Main layout wrapper

├── pages/

│   ├── LandingPage.tsx             # Marketing + signup

│   ├── DashboardPage.tsx           # User home

│   ├── DocumentsPage.tsx           # Document management

│   ├── StudyPage.tsx               # Study interface

│   ├── SubscriptionPage.tsx        # Billing management

│   └── SettingsPage.tsx            # User settings

├── hooks/

│   ├── useAuth.tsx                 # Authentication state

│   ├── useCredits.tsx              # Credit management

│   ├── useDocuments.tsx            # Document operations

│   └── useAIGeneration.tsx         # AI generation state

├── services/

│   ├── api.ts                      # API client

│   ├── auth.ts                     # Authentication service

│   └── stripe.ts                   # Stripe integration

├── stores/

│   └── authStore.ts                # Zustand global state

├── types/

│   └── index.ts                    # TypeScript definitions

└── utils/

    ├── constants.ts                # App constants

    └── helpers.ts                  # Utility functions

```



### Design System

- **Color Palette:** Dark space background (#0a0a0a) with purple accents (#8b5cf6, #a855f7)

- **Typography:** Clean, readable fonts with proper hierarchy

- **Interactive Elements:** Cursor pointer on hover, subtle animations

- **Material UI Inspiration:** Elevated cards, smooth transitions, consistent spacing



### Detailed Component Implementation Specifications



#### Common Components



##### Button Component (`frontend/src/components/common/Button.tsx`)

```typescript

// Implementation: Create a file frontend/src/components/common/Button.tsx

// This component should accept the ButtonProps interface from shared/types.ts



interface ButtonProps extends BaseComponentProps {

  children: React.ReactNode;

  onClick?: () => void;

  variant?: 'primary' | 'secondary' | 'danger'; // defaults to 'primary'

  size?: 'sm' | 'md' | 'lg'; // defaults to 'md'

  isLoading?: boolean; // if true, show a spinner and disable the button

  disabled?: boolean;

  type?: 'button' | 'submit' | 'reset'; // defaults to 'button'

  className?: string; // for additional Tailwind classes

}



// Styling Requirements:

// - Primary variant: bg-primary-500 hover:bg-primary-600 text-white

// - Secondary variant: border-2 border-primary-500 text-primary-500 hover:bg-primary-50

// - Danger variant: bg-red-500 hover:bg-red-600 text-white

// - Small: px-3 py-1.5 text-sm

// - Medium: px-4 py-2 text-base

// - Large: px-6 py-3 text-lg

// - Hover effects: hover:scale-105 transition-all duration-200

// - Loading state: Show spinner icon, disable pointer events

// - Disabled state: opacity-50 cursor-not-allowed

```



##### Input Component (`frontend/src/components/common/Input.tsx`)

```typescript

// Implementation: Create a file frontend/src/components/common/Input.tsx

// This component should accept the InputProps interface from shared/types.ts



interface InputProps extends BaseComponentProps {

  label?: string;

  placeholder?: string;

  value: string;

  onChange: (value: string) => void;

  type?: 'text' | 'email' | 'password' | 'number';

  error?: string;

  required?: boolean;

  disabled?: boolean;

}



// Implementation Requirements:

// - Label: Display above input if provided, add red asterisk if required

// - Input styling: border-2 border-gray-300 focus:border-primary-500 rounded-md

// - Error state: border-red-500, display error message below input in red

// - Dark theme: bg-background-secondary text-text-primary

// - Accessibility: proper aria-labels, aria-describedby for errors

```



##### Modal Component (`frontend/src/components/common/Modal.tsx`)

```typescript

// Implementation: Create a file frontend/src/components/common/Modal.tsx

// This component should accept the ModalProps interface from shared/types.ts



interface ModalProps extends BaseComponentProps {

  isOpen: boolean;

  onClose: () => void;

  title?: string;

  children: React.ReactNode;

  size?: 'sm' | 'md' | 'lg' | 'xl';

}



// Implementation Requirements:

// - Backdrop: fixed inset-0 bg-black bg-opacity-50 z-50

// - Modal container: centered, max-height with scroll if needed

// - Size variants: sm(max-w-md), md(max-w-lg), lg(max-w-2xl), xl(max-w-4xl)

// - Close button: X icon in top-right corner

// - Animation: fade-in/fade-out with Framer Motion

// - Escape key to close, click backdrop to close

// - Focus trap for accessibility

```



#### Document Components



##### Document Viewer (`frontend/src/components/documents/DocumentViewer.tsx`)

```typescript

interface DocumentViewerProps {

  documentId: string;

  onGenerateFlashcards: () => void;

  onGenerateQuiz: () => void;

}



// Implementation Requirements:

// - Use PDF.js or react-pdf for PDF viewing

// - Floating action buttons positioned bottom-right of viewer

// - Buttons: "Generate Flashcards" (primary) and "Generate Quiz" (secondary)

// - Maintain document scroll position during generation

// - Loading state while document loads

// - Error state for unsupported files or loading failures

// - Responsive: stack buttons vertically on mobile

```



##### Document Selector (`frontend/src/components/documents/DocumentSelector.tsx`)

```typescript

interface DocumentSelectorProps {

  documents: DocumentMetadata[];

  selectedIds: string[];

  onSelectionChange: (ids: string[]) => void;

  maxSelections?: number; // defaults to 10

}



// Implementation Requirements:

// - Vertical scrollable list with max-height-96

// - Each item: checkbox, filename, file type icon, file size, upload date

// - Checkbox styling: Tailwind forms plugin or custom styled

// - File type icons: PDF, DOCX, TXT, PPTX icons from react-icons

// - Selection limit enforcement: disable checkboxes when maxSelections reached

// - Select all/deselect all buttons at top

// - Search/filter functionality: filter by filename

// - Empty state: "No documents found" with upload button

```



##### Document Upload (`frontend/src/components/documents/DocumentUpload.tsx`)

```typescript

interface DocumentUploadProps {

  onUploadComplete: (document: DocumentMetadata) => void;

  onUploadError: (error: string) => void;

  acceptedTypes?: DocumentFileType[];

  maxFileSize?: number; // in bytes, defaults to 10MB

}



// Implementation Requirements:

// - Drag and drop zone: dashed border, hover effects

// - File input hidden, triggered by click or drop

// - File validation: type, size, before upload

// - Upload progress bar using upload progress events

// - Multiple file upload support

// - Error display for rejected files

// - Success feedback with uploaded file details

// - Styling: border-dashed border-2 border-gray-300 hover:border-primary-500

```



#### Study Components



##### Flashcard Viewer (`frontend/src/components/study/FlashcardViewer.tsx`)

```typescript

interface FlashcardViewerProps {

  flashcards: Flashcard[];

  onFlag: (id: string) => void;

  onUpdateReviewCount?: (id: string) => void;

}



// Implementation Requirements:

// - Card display: 3D flip animation using CSS transforms

// - Keyboard navigation:

//   * Space bar: flip current card (show back if front, front if back)

//   * Left arrow: previous card (always show front)

//   * Right arrow: next card (always show front)

//   * F key: toggle flag on current card

//   * R key: mark as reviewed (increment times_reviewed)

// - Visual indicators:

//   * Flag icon visible when card is flagged

//   * Progress bar showing position (e.g., "3 of 15")

//   * Difficulty indicator if available

// - Card styling: elevated shadow, rounded corners, min-height for consistency

// - Navigation controls: previous/next buttons for non-keyboard users

// - End of set: "Review complete" screen with study statistics

```



##### Quiz Interface (`frontend/src/components/study/QuizInterface.tsx`)

```typescript

interface QuizInterfaceProps {

  questions: QuizQuestion[];

  onQuizComplete: (results: QuizResults) => void;

  timePerQuestion?: number; // seconds, optional timer

}



interface QuizResults {

  totalQuestions: number;

  correctAnswers: number;

  timeSpent: number;

  questionResults: { questionId: string; correct: boolean; timeSpent: number }[];

}



// Implementation Requirements:

// - Question types implementation:

//   1. Multiple Choice: Radio buttons, single selection

//   2. Select All: Checkboxes, multiple selections allowed

//   3. True/False: Two large buttons for True/False

//   4. Short Answer: Text input with trim and toLowerCase comparison

// - Immediate feedback: show correct/incorrect after each answer

// - Explanation display: show explanation after answering if available

// - Progress tracking: progress bar, question X of Y

// - Timer: countdown timer per question if timePerQuestion provided

// - Navigation: "Next Question" button, disabled until answered

// - Final results: score percentage, time taken, review incorrect answers option

// - Accessibility: proper ARIA labels, keyboard navigation

```



##### Study Set Editor (`frontend/src/components/study/StudySetEditor.tsx`)

```typescript

interface StudySetEditorProps {

  studySet?: StudySet; // undefined for new study set

  onSave: (studySet: StudySet) => void;

  onCancel: () => void;

  allowAIGeneration?: boolean;

}



// Implementation Requirements:

// - Basic info form: study set name, type (flashcards/quiz)

// - Manual content creation:

//   * Add/edit/delete individual flashcards or quiz questions

//   * Rich text editor for card content and explanations

//   * Question type selector for quizzes

// - AI generation section:

//   * Document selector (reuse DocumentSelector component)

//   * Custom prompt textarea

//   * Generate buttons (separate for flashcards/quiz)

//   * Credit cost display before generation

// - Content management:

//   * Drag-and-drop reordering of items

//   * Bulk edit/delete operations

//   * Import/export functionality

// - Auto-save: save draft every 30 seconds

// - Validation: ensure required fields before save

```



#### Credit & Subscription Components



##### Credit Balance (`frontend/src/components/common/CreditBalance.tsx`)

```typescript

interface CreditBalanceProps {

  credits: number;

  showDetails?: boolean; // show breakdown/history link

  size?: 'sm' | 'md' | 'lg';

}



// Implementation Requirements:

// - Display current credit count with coin/credit icon

// - Color coding: green (>50), yellow (10-50), red (<10)

// - Click to show credit history modal if showDetails=true

// - Responsive sizing based on size prop

// - Real-time updates when credits change

// - Animation on credit count changes

```



##### Pricing Card (`frontend/src/components/subscription/PricingCard.tsx`)

```typescript

interface PricingCardProps {

  tier: 'Free' | 'Basic' | 'Pro';

  price: number;

  credits: number;

  features: string[];

  isCurrentPlan?: boolean;

  onSelect: () => void;

}



// Implementation Requirements:

// - Card layout: title, price, credit count, feature list, action button

// - Current plan indication: different styling, "Current Plan" badge

// - Feature list: checkmark icons for included features

// - Call-to-action button: "Upgrade" or "Select Plan"

// - Hover effects: subtle elevation change

// - Responsive: stack on mobile, grid on desktop

```



---



## 7. Backend Technical Specification



### Technology Stack

- **Express.js** with **TypeScript**

- **Supabase Client** for database and auth

- **bcrypt** for password hashing

- **Stripe** for payment processing

- **Multer** for file uploads

- **pdf-parse**, **mammoth.js**, **node-xlsx** for document processing



### Service Architecture

```

src/

├── api/

│   ├── routes/

│   │   ├── auth.ts                 # Authentication routes

│   │   ├── documents.ts            # Document management

│   │   ├── studySets.ts           # Study set operations

│   │   ├── ai.ts                  # AI generation endpoints

│   │   ├── credits.ts             # Credit management

│   │   └── stripe.ts              # Payment processing

│   ├── controllers/

│   │   ├── authController.ts       # Auth business logic

│   │   ├── documentController.ts   # Document operations

│   │   ├── aiController.ts        # AI generation logic

│   │   └── stripeController.ts    # Payment logic

│   └── middleware/

│       ├── auth.ts                # JWT authentication & resource ownership

│       ├── creditCheck.ts         # Credit validation & atomic operations

│       ├── rateLimit.ts           # API rate limiting & DDoS protection

│       ├── validation.ts          # Input validation & sanitization

│       ├── security.ts            # Security headers & CORS

│       └── audit.ts               # Audit logging for sensitive operations

├── services/

│   ├── supabaseService.ts         # Database operations

│   ├── aiService.ts               # OpenRouter integration

│   ├── documentService.ts         # File processing

│   ├── creditService.ts           # Credit management

│   └── stripeService.ts           # Payment processing

├── utils/

│   ├── fileProcessor.ts           # Document text extraction

│   ├── creditCalculator.ts        # Dynamic credit pricing

│   └── validators.ts              # Input validation schemas

├── types/

│   └── index.ts                   # Shared type definitions

├── config/

│   ├── database.ts                # Supabase configuration

│   ├── stripe.ts                  # Stripe configuration

│   └── environment.ts             # Environment variables

└── app.ts                         # Express app setup

```



### Key Backend Features



#### Enhanced Credit Service with Database Security

```typescript

// Secure credit system using Supabase stored procedures

class CreditService {

  async getOperationCost(operationType: string): Promise<number> {

    const { data, error } = await supabase

      .from('ai_operation_costs')

      .select('credits_required')

      .eq('operation_type', operationType)

      .eq('is_active', true)

      .single();



    if (error || !data) {

      throw new Error(`Invalid operation type: ${operationType}`);

    }



    return data.credits_required;

  }



  // Atomic credit deduction using stored procedure

  async deductCredits(

    userId: string,

    operationType: string,

    metadata?: any,

    studySetId?: string

  ): Promise<{ success: boolean; remainingCredits: number; message: string }> {

    const cost = await this.getOperationCost(operationType);



    const { data, error } = await supabase.rpc('deduct_credits', {

      p_user_id: userId,

      p_credits_to_deduct: cost,

      p_operation_type: operationType,

      p_description: `AI generation: ${operationType}`,

      p_metadata: metadata || {},

      p_study_set_id: studySetId || null

    });



    if (error) {

      throw new CreditError('Credit deduction failed', error);

    }



    return data[0]; // Returns {success, remaining_credits, message}

  }



  // Add credits for purchases/subscriptions

  async addCredits(

    userId: string,

    creditsToAdd: number,

    source: string,

    referenceId?: string

  ): Promise<{ success: boolean; newBalance: number; message: string }> {

    const { data, error } = await supabase.rpc('add_credits', {

      p_user_id: userId,

      p_credits_to_add: creditsToAdd,

      p_source: source,

      p_reference_id: referenceId || null

    });



    if (error) {

      throw new CreditError('Credit addition failed', error);

    }



    return data[0];

  }



  // Get user's current credit balance

  async getUserCredits(userId: string): Promise<number> {

    const { data, error } = await supabase

      .from('users')

      .select('credits_remaining')

      .eq('id', userId)

      .single();



    if (error || !data) {

      throw new Error('User not found');

    }



    return data.credits_remaining;

  }



  // Validate credit operation before execution

  async validateCreditOperation(userId: string, operationType: string): Promise<boolean> {

    const [userCredits, requiredCredits] = await Promise.all([

      this.getUserCredits(userId),

      this.getOperationCost(operationType)

    ]);



    return userCredits >= requiredCredits;

  }

}



// Custom error class for credit operations

class CreditError extends Error {

  constructor(message: string, public originalError?: any) {

    super(message);

    this.name = 'CreditError';

  }

}

```



#### Precise AI Service Implementation with OpenRouter



##### AI Service (`backend/src/services/aiService.ts`)

```typescript

import OpenRouter from '@openrouter/ai';

import { Flashcard, QuizQuestion, QuestionType } from '../../../shared/types';



class AIService {

  private openrouter: OpenRouter;



  constructor() {

    this.openrouter = new OpenRouter({

      apiKey: process.env.OPENROUTER_API_KEY!,

      defaultModel: 'google/gemini-2.0-flash-exp'

    });

  }



  /**

   * Generate flashcards from document content

   * CRITICAL: The system prompt below is optimized for consistent JSON output

   */

  async generateFlashcards(

    content: string,

    count: number = 10,

    customPrompt?: string

  ): Promise<Flashcard[]> {

    const systemPrompt = `You are an expert study assistant. Your task is to generate high-quality flashcards from the provided text.

Analyze the content and create flashcards focusing on key terms, concepts, and important facts.



**CRITICAL INSTRUCTIONS:**

1. The output MUST be a valid JSON array of objects.

2. Each object in the array represents a single flashcard and must have exactly two properties: "front" and "back".

3. The "front" should be a concise term or question (max 100 characters).

4. The "back" should be the corresponding definition or answer (max 300 characters).

5. Do not include any text, explanations, or markdown outside of the JSON array.

6. Ensure the JSON is properly formatted and parseable.



**Example Output:**

[

  {

    "front": "What is Row Level Security (RLS)?",

    "back": "A database security feature that restricts data access for a given user at the row level, ensuring users can only see their own data."

  },

  {

    "front": "What is bcrypt?",

    "back": "A password-hashing function designed to be slow to protect against brute-force search attacks."

  }

]



Generate exactly ${count} flashcards from the following content:`;



    const userPrompt = customPrompt

      ? `${customPrompt}\n\n---\nContent:\n${content}`

      : content;



    try {

      const response = await this.openrouter.chat.completions.create({

        model: 'google/gemini-2.0-flash-exp',

        messages: [

          { role: 'system', content: systemPrompt },

          { role: 'user', content: userPrompt }

        ],

        max_tokens: 3000,

        temperature: 0.7

      });



      const aiResponse = response.choices[0].message.content;

      return this.parseFlashcardResponse(aiResponse);

    } catch (error) {

      throw new Error(`AI flashcard generation failed: ${error.message}`);

    }

  }



  /**

   * Generate quiz questions from document content

   * CRITICAL: Supports multiple question types with structured JSON output

   */

  async generateQuiz(

    content: string,

    questionTypes: QuestionType[],

    count: number = 5,

    customPrompt?: string

  ): Promise<QuizQuestion[]> {

    const typeInstructions = {

      multiple_choice: 'Multiple choice questions with exactly 4 options, only 1 correct',

      select_all: 'Select all that apply questions with 4-6 options, multiple correct answers',

      true_false: 'True/false questions with explanations',

      short_answer: 'Short answer questions expecting 1-3 word responses'

    };



    const selectedInstructions = questionTypes

      .map(type => `- ${typeInstructions[type]}`)

      .join('\n');



    const systemPrompt = `You are an expert assessment creator. Generate quiz questions from the provided content.



**CRITICAL INSTRUCTIONS:**

1. Output MUST be a valid JSON array of question objects.

2. Each question object must have these exact properties:

   - "question_text": string (the question, max 200 characters)

   - "question_type": string (one of: "multiple_choice", "select_all", "true_false", "short_answer")

   - "options": array of strings (for multiple_choice/select_all) or null (for true_false/short_answer)

   - "correct_answers": array of strings (always an array, even for single answers)

   - "explanation": string (optional, brief explanation of the answer)



**Question Type Requirements:**

${selectedInstructions}



**Example Output:**

[

  {

    "question_text": "Which of the following are benefits of using TypeScript?",

    "question_type": "select_all",

    "options": ["Type safety", "Better IDE support", "Slower compilation", "Runtime performance boost"],

    "correct_answers": ["Type safety", "Better IDE support"],

    "explanation": "TypeScript provides compile-time type checking and enhanced IDE features, but doesn't improve runtime performance."

  },

  {

    "question_text": "What is the purpose of useState in React?",

    "question_type": "short_answer",

    "options": null,

    "correct_answers": ["state management", "managing state", "state"],

    "explanation": "useState is a React Hook for managing component state."

  }

]



Generate exactly ${count} questions distributed across the specified question types:`;



    const userPrompt = customPrompt

      ? `${customPrompt}\n\n---\nContent:\n${content}`

      : content;



    try {

      const response = await this.openrouter.chat.completions.create({

        model: 'google/gemini-2.0-flash-exp',

        messages: [

          { role: 'system', content: systemPrompt },

          { role: 'user', content: userPrompt }

        ],

        max_tokens: 4000,

        temperature: 0.7

      });



      const aiResponse = response.choices[0].message.content;

      return this.parseQuizResponse(aiResponse);

    } catch (error) {

      throw new Error(`AI quiz generation failed: ${error.message}`);

    }

  }



  /**

   * Parse and validate flashcard response from AI

   * CRITICAL: Robust error handling for malformed JSON

   */

  private parseFlashcardResponse(response: string): Flashcard[] {

    try {

      // Clean the response - remove any non-JSON content

      const jsonMatch = response.match(/\[[\s\S]*\]/);

      if (!jsonMatch) {

        throw new Error('No valid JSON array found in AI response');

      }



      const jsonString = jsonMatch[0];

      const parsed = JSON.parse(jsonString);



      if (!Array.isArray(parsed)) {

        throw new Error('AI response is not an array');

      }



      // Validate each flashcard object

      const flashcards: Flashcard[] = parsed.map((item, index) => {

        if (!item.front || !item.back) {

          throw new Error(`Flashcard ${index} missing required fields`);

        }



        return {

          id: '', // Will be set by database

          study_set_id: '', // Will be set by controller

          front: String(item.front).substring(0, 500), // Truncate if too long

          back: String(item.back).substring(0, 1000),

          is_flagged: false,

          is_ai_generated: true,

          difficulty_level: null,

          times_reviewed: 0,

          last_reviewed_at: null

        };

      });



      return flashcards;

    } catch (error) {

      throw new Error(`Failed to parse flashcard response: ${error.message}`);

    }

  }



  /**

   * Parse and validate quiz response from AI

   * CRITICAL: Validates question types and answer formats

   */

  private parseQuizResponse(response: string): QuizQuestion[] {

    try {

      // Clean the response - remove any non-JSON content

      const jsonMatch = response.match(/\[[\s\S]*\]/);

      if (!jsonMatch) {

        throw new Error('No valid JSON array found in AI response');

      }



      const jsonString = jsonMatch[0];

      const parsed = JSON.parse(jsonString);



      if (!Array.isArray(parsed)) {

        throw new Error('AI response is not an array');

      }



      // Validate each question object

      const questions: QuizQuestion[] = parsed.map((item, index) => {

        if (!item.question_text || !item.question_type || !item.correct_answers) {

          throw new Error(`Question ${index} missing required fields`);

        }



        const validTypes: QuestionType[] = ['multiple_choice', 'select_all', 'true_false', 'short_answer'];

        if (!validTypes.includes(item.question_type)) {

          throw new Error(`Question ${index} has invalid question_type`);

        }



        // Validate question type specific requirements

        if (['multiple_choice', 'select_all'].includes(item.question_type) && !item.options) {

          throw new Error(`Question ${index} of type ${item.question_type} requires options`);

        }



        return {

          id: '', // Will be set by database

          study_set_id: '', // Will be set by controller

          question_text: String(item.question_text).substring(0, 500),

          question_type: item.question_type as QuestionType,

          options: item.options || null,

          correct_answers: Array.isArray(item.correct_answers)

            ? item.correct_answers.map(String)

            : [String(item.correct_answers)],

          explanation: item.explanation ? String(item.explanation).substring(0, 500) : null,

          is_ai_generated: true,

          difficulty_level: null,

          times_attempted: 0,

          times_correct: 0

        };

      });



      return questions;

    } catch (error) {

      throw new Error(`Failed to parse quiz response: ${error.message}`);

    }

  }

}



export default AIService;

```



##### Document Controller Implementation (`backend/src/controllers/documentController.ts`)

```typescript

import { Request, Response } from 'express';

import multer from 'multer';

import { DocumentProcessor } from '../services/documentService';

import { supabaseService } from '../services/supabaseService';

import { DocumentFileType } from '../../../shared/types';



// Configure multer for file uploads

const upload = multer({

  storage: multer.memoryStorage(),

  limits: {

    fileSize: 10 * 1024 * 1024, // 10MB limit

  },

  fileFilter: (req, file, cb) => {

    const allowedTypes = [

      'application/pdf',

      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',

      'text/plain',

      'application/vnd.openxmlformats-officedocument.presentationml.presentation'

    ];

    

    if (allowedTypes.includes(file.mimetype)) {

      cb(null, true);

    } else {

      cb(new Error('Unsupported file type'), false);

    }

  }

});



/**

 * Upload document controller with complete error handling

 * CRITICAL: Step-by-step implementation with edge cases

 */

export const uploadDocument = [

  upload.single('document'), // Multer middleware

  async (req: Request, res: Response) => {

    try {

      // Step 1: Check if file was uploaded

      if (!req.file) {

        return res.status(400).json({

          success: false,

          error: 'No file uploaded'

        });

      }



      const userId = req.user.id;

      const file = req.file;



      // Step 2: Initialize document processor

      const processor = new DocumentProcessor();



      // Step 3: Process file and extract text

      let extractedText: string;

      try {

        extractedText = await processor.processFile(file);

      } catch (processingError) {

        console.error('File processing error:', processingError);

        return res.status(415).json({

          success: false,

          error: 'Unsupported file type or corrupted file'

        });

      }



      // Step 4: Upload original file to Supabase Storage

      const fileName = `${userId}/${Date.now()}_${file.originalname}`;

      let storagePath: string;

      

      try {

        storagePath = await supabaseService.uploadFile(fileName, file.buffer, file.mimetype);

      } catch (storageError) {

        console.error('Storage upload error:', storageError);

        return res.status(500).json({

          success: false,

          error: 'Failed to store file'

        });

      }



      // Step 5: Insert document metadata and content into database

      try {

        const documentData = {

          user_id: userId,

          filename: file.originalname,

          file_type: getFileTypeFromMimetype(file.mimetype),

          file_size: file.size,

          content_text: extractedText,

          supabase_storage_path: storagePath,

          is_processed: true,

          processing_error: null

        };



        const document = await supabaseService.createDocument(documentData);



        return res.status(201).json({

          success: true,

          data: document,

          message: 'Document uploaded and processed successfully'

        });



      } catch (dbError) {

        console.error('Database error:', dbError);

        

        // Step 6: Cleanup - delete uploaded file if database insert fails

        try {

          await supabaseService.deleteFile(storagePath);

        } catch (cleanupError) {

          console.error('Failed to cleanup file after database error:', cleanupError);

        }



        return res.status(500).json({

          success: false,

          error: 'Failed to save document metadata'

        });

      }



    } catch (error) {

      console.error('Unexpected error in uploadDocument:', error);

      return res.status(500).json({

        success: false,

        error: 'Internal server error'

      });

    }

  }

];



/**

 * Helper function to map MIME types to our DocumentFileType

 */

function getFileTypeFromMimetype(mimetype: string): DocumentFileType {

  const typeMap = {

    'application/pdf': 'pdf' as const,

    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'docx' as const,

    'text/plain': 'txt' as const,

    'application/vnd.openxmlformats-officedocument.presentationml.presentation': 'pptx' as const

  };

  

  return typeMap[mimetype] || 'txt';

}

```



#### Document Processing Pipeline

```typescript

// Multi-format document processing

class DocumentProcessor {

  async processFile(file: Express.Multer.File): Promise<string> {

    switch (file.mimetype) {

      case 'application/pdf':

        return this.processPDF(file.buffer);

      case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':

        return this.processDocx(file.buffer);

      case 'text/plain':

        return this.processText(file.buffer);

      case 'application/vnd.openxmlformats-officedocument.presentationml.presentation':

        return this.processPPTX(file.buffer);

      default:

        throw new Error('Unsupported file type');

    }

  }



  private async processPDF(buffer: Buffer): Promise<string> {

    const pdfParse = await import('pdf-parse');

    const data = await pdfParse.default(buffer);

    return data.text;

  }



  private async processDocx(buffer: Buffer): Promise<string> {

    const mammoth = await import('mammoth');

    const result = await mammoth.extractRawText({ buffer });

    return result.value;

  }



  private async processText(buffer: Buffer): Promise<string> {

    return buffer.toString('utf-8');

  }



  private async processPPTX(buffer: Buffer): Promise<string> {

    // Implementation for PPTX text extraction

    // Use a library like 'pptx2json' or 'node-pptx'

    throw new Error('PPTX processing not yet implemented');

  }

}

```



---



## 8. Core Features & User Flows



### Primary User Flows



#### 1. Document Upload & Management Flow

```mermaid

flowchart TD

    A[User visits Documents page] --> B[Drag & drop or click upload]

    B --> C[Select PDF/DOCX/TXT/PPTX file]

    C --> D[Backend processes file]

    D --> E[Extract text content]

    E --> F[Store document + metadata]

    F --> G[Show in document library]

    G --> H[Click document to view]

    H --> I[Embedded viewer with AI buttons]

```



#### 2. AI Generation Flow

```mermaid

flowchart TD

    A[User clicks Generate Flashcards/Quiz] --> B[Multi-document selector opens]

    B --> C[Select documents with checkboxes]

    C --> D[Choose generation options]

    D --> E[Enter custom prompt (optional)]

    E --> F[Submit generation request]

    F --> G{Check credits}

    G -->|Insufficient| H[Show upgrade/purchase modal]

    G -->|Sufficient| I[Combine document content]

    I --> J[Send to OpenRouter API]

    J --> K[Parse AI response]

    K --> L[Store generated content]

    L --> M[Deduct credits]

    M --> N[Redirect to study interface]

```



#### 3. Study Flow

```mermaid

flowchart TD

    A[User selects study set] --> B{Content type}

    B -->|Flashcards| C[Flashcard viewer]

    B -->|Quiz| D[Quiz interface]

    

    C --> E[Show front of card]

    E --> F[Space bar to flip]

    F --> G[Arrow keys to navigate]

    G --> H[Flag difficult cards]

    

    D --> I[Show question]

    I --> J[User selects answer]

    J --> K[Immediate feedback]

    K --> L[Track score]

```



### Feature Specifications



#### Flashcard Interface

- **Keyboard Navigation:**

  - Space bar: Flip card

  - Left/Right arrows: Navigate between cards

  - F key: Flag/unflag current card

- **Visual State:**

  - Always show front when navigating to new card

  - Smooth flip animation using CSS transforms

  - Flag indicator visible when card is flagged

  - Progress indicator showing position in set



#### Quiz Interface

- **Question Types:**

  1. **Multiple Choice:** Single correct answer from 4 options

  2. **Select All That Apply:** Multiple correct answers

  3. **True/False:** Binary choice with explanations

  4. **Short Answer:** Text input with keyword matching

- **Features:**

  - Immediate feedback after each question

  - Optional timer per question

  - Progress tracking throughout quiz

  - Final score summary with review option



#### Document Viewer Integration

- **Embedded Viewer:** PDF.js or similar for in-app viewing

- **Generation Buttons:** Floating action buttons over viewer

- **Quick Generation:** Single-document generation with one click

- **Context Preservation:** Maintain viewer position during generation



---



## 9. UI/UX Design System



### Theme Configuration

```typescript

// Tailwind theme extension

const theme = {

  colors: {

    primary: {

      50: '#faf5ff',

      500: '#8b5cf6',

      600: '#7c3aed',

      700: '#6d28d9',

      900: '#4c1d95'

    },

    background: {

      primary: '#0a0a0a',

      secondary: '#1a1a1a',

      tertiary: '#2a2a2a'

    },

    text: {

      primary: '#ffffff',

      secondary: '#a3a3a3',

      muted: '#737373'

    }

  }

}

```



### Component Design Principles

- **Material UI Inspiration:** Elevated surfaces, consistent shadows

- **Interactive Feedback:** 

  - `cursor-pointer` on all clickable elements

  - Hover state changes (opacity, scale, color)

  - Focus states for accessibility

- **Consistent Spacing:** 8px grid system

- **Typography Scale:** Clear hierarchy with proper contrast



### Animation Guidelines

```css

/* Hover effects for buttons */

.btn-primary {

  @apply transition-all duration-200 ease-in-out;

  @apply hover:scale-105 hover:shadow-lg;

}



/* Card hover effects */

.card {

  @apply transition-transform duration-200;

  @apply hover:translate-y-[-2px];

}



/* Flashcard flip animation */

.flashcard {

  @apply transition-transform duration-300;

  transform-style: preserve-3d;

}

```



---



## 10. Development Setup Guide



### Prerequisites

- Node.js (v18 or later)

- npm or yarn

- Git

- Supabase account

- Stripe account

- OpenRouter API key



### Environment Configuration



#### Backend Environment Variables (All sensitive credentials stay server-side)

```bash

# Database & Authentication

SUPABASE_URL=your_supabase_project_url

SUPABASE_ANON_KEY=your_supabase_anon_key

SUPABASE_SERVICE_KEY=your_supabase_service_role_key  # CRITICAL: Never expose to frontend



# Payment Processing

STRIPE_SECRET_KEY=your_stripe_secret_key            # CRITICAL: Never expose to frontend

STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret    # CRITICAL: Never expose to frontend

STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key



# AI Services

OPENROUTER_API_KEY=your_openrouter_api_key         # CRITICAL: Never expose to frontend



# Security

JWT_SECRET=your_jwt_secret_key                     # CRITICAL: Never expose to frontend

BCRYPT_SALT_ROUNDS=12



# Server Configuration

PORT=5000

NODE_ENV=development

FRONTEND_URL=http://localhost:3000                 # For CORS configuration

```



#### Frontend Environment Variables (Only backend API endpoint)

```bash

# Frontend only knows about your backend - NO EXTERNAL SERVICE CREDENTIALS

VITE_API_BASE_URL=http://localhost:5000/api

# NO OTHER CREDENTIALS - Everything goes through your backend for security

```



### Setup Steps

1. **Clone repository:**

   ```bash

   git clone <repository_url>

   cd chewy-ai

   ```



2. **Install dependencies:**

   ```bash

   # Root dependencies

   npm install

   

   # Backend dependencies

   cd backend && npm install

   

   # Frontend dependencies

   cd ../frontend && npm install

   ```



3. **Database setup:**

   - Create Supabase project

   - Run migration scripts

   - Set up authentication policies



4. **Development servers:**

   ```bash

   # Start backend (from /backend)

   npm run dev

   

   # Start frontend (from /frontend)

   npm run dev

   ```



### Build Process

```bash

# Production build

npm run build



# This will:

# 1. Build frontend with Vite

# 2. Copy build output to /backend/public

# 3. Build backend TypeScript

# 4. Create single deployable server

```



---



## 11. Deployment Guide



### Production Architecture

- Single ExpressJS server serving both API and static files

- Environment-based configuration

- Docker containerization

- Automated build pipeline



### Production Deployment (No Docker)

```bash

# Simple Node.js deployment

# 1. Build the application

npm run build



# 2. Start the production server

npm start

```



### Deployment Steps

1. **Environment setup:** Configure production environment variables on server

2. **Install dependencies:** `npm install --production`

3. **Build application:** `npm run build` (builds frontend to backend/public)

4. **Start server:** `npm start` or use PM2 for process management

5. **Configure domain:** Set up reverse proxy (Nginx) and SSL

6. **Monitor:** Set up logging and monitoring tools



---



## 12. Security Implementation



### Security Architecture Overview



```mermaid

flowchart TD

    A[Frontend] --> B[Backend API Gateway]

    B --> C{Authentication Check}

    C -->|Valid JWT| D[Authorization Check]

    C -->|Invalid| E[Return 401]

    D -->|Authorized| F[Business Logic]

    D -->|Unauthorized| G[Return 403]

    F --> H[Supabase RLS Check]

    F --> I[External API Calls]

    H --> J[Database Operation]

    I --> K[OpenRouter/Stripe]



    subgraph "Security Layers"

        L[Rate Limiting]

        M[Input Validation]

        N[Credit Validation]

        O[CORS Protection]

    end



    B --> L

    B --> M

    F --> N

    B --> O

```



### Authentication & Authorization



#### Frontend Authentication Pattern

```typescript

// Frontend NEVER handles external service credentials

class AuthService {

  async login(email: string, password: string): Promise<AuthResult> {

    // Only communicates with your backend

    const response = await fetch('/api/auth/login', {

      method: 'POST',

      headers: { 'Content-Type': 'application/json' },

      body: JSON.stringify({ email, password })

    });



    if (response.ok) {

      const { token, user } = await response.json();

      // Store JWT securely (httpOnly cookie preferred)

      this.setAuthToken(token);

      return { success: true, user };

    }



    return { success: false, error: 'Invalid credentials' };

  }



  // All subsequent requests include JWT

  async makeAuthenticatedRequest(endpoint: string, options: RequestInit) {

    return fetch(`/api${endpoint}`, {

      ...options,

      headers: {

        ...options.headers,

        'Authorization': `Bearer ${this.getToken()}`

      }

    });

  }

}

```



#### Backend Authentication Middleware

```typescript

const authenticateJWT = async (req: Request, res: Response, next: NextFunction) => {

  const authHeader = req.headers.authorization;

  const token = authHeader && authHeader.split(' ')[1];



  if (!token) {

    return res.status(401).json({ error: 'Access token required' });

  }



  try {

    const decoded = jwt.verify(token, process.env.JWT_SECRET!);

    req.user = decoded as JWTPayload;

    next();

  } catch (error) {

    return res.status(403).json({ error: 'Invalid token' });

  }

};

```



#### Resource Ownership Verification

```typescript

// Resource ownership verification

const verifyResourceOwnership = (resourceType: 'study_set' | 'document') => {

  return async (req: Request, res: Response, next: NextFunction) => {

    const userId = req.user.id;

    const resourceId = req.params.id;



    const { data, error } = await supabase

      .from(resourceType === 'study_set' ? 'study_sets' : 'documents')

      .select('user_id')

      .eq('id', resourceId)

      .eq('user_id', userId)

      .single();



    if (error || !data) {

      return res.status(404).json({ error: 'Resource not found' });

    }



    next();

  };

};



// Usage: Protect routes that access user resources

app.delete('/api/study-sets/:id',

  authenticateJWT,

  verifyResourceOwnership('study_set'),

  deleteStudySet

);

```



### Input Validation & Sanitization



```typescript

import { body, validationResult } from 'express-validator';

import DOMPurify from 'isomorphic-dompurify';



// Comprehensive validation middleware

const validateStudySetCreation = [

  body('name')

    .trim()

    .isLength({ min: 1, max: 255 })

    .escape()

    .withMessage('Name must be 1-255 characters'),

  body('document_ids')

    .isArray({ min: 1, max: 10 })

    .withMessage('Must select 1-10 documents'),

  body('custom_prompt')

    .optional()

    .trim()

    .isLength({ max: 1000 })

    .customSanitizer((value) => DOMPurify.sanitize(value))

];



const handleValidationErrors = (req: Request, res: Response, next: NextFunction) => {

  const errors = validationResult(req);

  if (!errors.isEmpty()) {

    return res.status(400).json({

      error: 'Validation failed',

      details: errors.array()

    });

  }

  next();

};

```



### Credit System Security



```typescript

class CreditService {

  // Atomic credit operations with transaction safety

  async deductCredits(userId: string, operationType: string, metadata?: any): Promise<boolean> {

    const cost = await this.getOperationCost(operationType);



    // Use Supabase RPC for atomic transaction

    const { data, error } = await supabase.rpc('deduct_credits', {

      user_id: userId,

      credits_to_deduct: cost,

      operation_type: operationType,

      metadata: metadata || {}

    });



    if (error || !data) {

      throw new CreditError('Insufficient credits or operation failed');

    }



    return true;

  }



  // Prevent credit manipulation

  async validateCreditOperation(userId: string, operationType: string): Promise<boolean> {

    const userCredits = await this.getUserCredits(userId);

    const requiredCredits = await this.getOperationCost(operationType);



    return userCredits >= requiredCredits;

  }

}



// Credit validation middleware

const creditCheck = async (req: Request, res: Response, next: NextFunction) => {

  try {

    const operationType = req.body.operation_type || 'ai_generation';

    const isValid = await creditService.validateCreditOperation(req.user.id, operationType);



    if (!isValid) {

      return res.status(402).json({

        error: 'Insufficient credits',

        required: await creditService.getOperationCost(operationType),

        available: await creditService.getUserCredits(req.user.id)

      });

    }



    next();

  } catch (error) {

    res.status(500).json({ error: 'Credit validation failed' });

  }

};

```



### Rate Limiting & DDoS Protection



```typescript

import rateLimit from 'express-rate-limit';

import slowDown from 'express-slow-down';



// Different limits for different operations

const generalLimit = rateLimit({

  windowMs: 15 * 60 * 1000, // 15 minutes

  max: 100, // 100 requests per window

  message: 'Too many requests'

});



const aiGenerationLimit = rateLimit({

  windowMs: 60 * 60 * 1000, // 1 hour

  max: 50, // 50 AI generations per hour

  keyGenerator: (req) => req.user?.id || req.ip,

  message: 'AI generation rate limit exceeded'

});



const speedLimiter = slowDown({

  windowMs: 15 * 60 * 1000, // 15 minutes

  delayAfter: 50, // allow 50 requests per window at full speed

  delayMs: 500 // add 500ms delay per request after delayAfter

});



// Apply to routes

app.use('/api', generalLimit, speedLimiter);

app.use('/api/ai/*', aiGenerationLimit);

```



### Database Security (Supabase RLS)



#### Complete RLS Policy Implementation



```sql

-- Users Table Security

ALTER TABLE users ENABLE ROW LEVEL SECURITY;



-- Users can only read their own profile

CREATE POLICY "Users can read own profile" ON users

    FOR SELECT USING (auth.uid() = id);



-- Users can update their own non-critical fields

CREATE POLICY "Users can update own profile" ON users

    FOR UPDATE USING (auth.uid() = id)

    WITH CHECK (

        auth.uid() = id AND

        -- Prevent direct manipulation of critical fields

        OLD.credits_remaining = NEW.credits_remaining AND

        OLD.subscription_tier = NEW.subscription_tier AND

        OLD.stripe_customer_id = NEW.stripe_customer_id

    );



-- Documents Table Security

ALTER TABLE documents ENABLE ROW LEVEL SECURITY;



CREATE POLICY "Users own documents access" ON documents

    FOR ALL USING (auth.uid() = user_id);



-- Study Sets Table Security

ALTER TABLE study_sets ENABLE ROW LEVEL SECURITY;



CREATE POLICY "Users own study sets access" ON study_sets

    FOR ALL USING (auth.uid() = user_id);



-- Flashcards Security (Inherit from study_set ownership)

ALTER TABLE flashcards ENABLE ROW LEVEL SECURITY;



CREATE POLICY "Users access flashcards through study sets" ON flashcards

    FOR ALL USING (

        EXISTS (

            SELECT 1 FROM study_sets

            WHERE study_sets.id = flashcards.study_set_id

            AND study_sets.user_id = auth.uid()

        )

    );



-- Quiz Questions Security (Inherit from study_set ownership)

ALTER TABLE quiz_questions ENABLE ROW LEVEL SECURITY;



CREATE POLICY "Users access quiz questions through study sets" ON quiz_questions

    FOR ALL USING (

        EXISTS (

            SELECT 1 FROM study_sets

            WHERE study_sets.id = quiz_questions.study_set_id

            AND study_sets.user_id = auth.uid()

        )

    );



-- Credit Transactions Security (Append-only audit table)

ALTER TABLE credit_transactions ENABLE ROW LEVEL SECURITY;



CREATE POLICY "Users can read own credit history" ON credit_transactions

    FOR SELECT USING (auth.uid() = user_id);



-- Prevent tampering with audit trail

CREATE POLICY "No updates to credit transactions" ON credit_transactions

    FOR UPDATE USING (FALSE);



CREATE POLICY "No deletes from credit transactions" ON credit_transactions

    FOR DELETE USING (FALSE);



-- AI Operation Costs (Read-only for users, admin-only modification)

ALTER TABLE ai_operation_costs ENABLE ROW LEVEL SECURITY;



CREATE POLICY "Authenticated users can read operation costs" ON ai_operation_costs

    FOR SELECT USING (auth.role() = 'authenticated');



CREATE POLICY "Only admin can modify operation costs" ON ai_operation_costs

    FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

```



#### Critical Stored Procedures



```sql

-- Atomic Credit Deduction with Enhanced Security

CREATE OR REPLACE FUNCTION deduct_credits(

    p_user_id UUID,

    p_credits_to_deduct INTEGER,

    p_operation_type TEXT,

    p_description TEXT DEFAULT NULL,

    p_metadata JSONB DEFAULT '{}',

    p_study_set_id UUID DEFAULT NULL

)

RETURNS TABLE(success BOOLEAN, remaining_credits INTEGER, message TEXT) AS $$

DECLARE

    current_credits INTEGER;

    operation_cost INTEGER;

    final_description TEXT;

BEGIN

    -- Validate operation type and get cost

    SELECT credits_required INTO operation_cost

    FROM ai_operation_costs

    WHERE operation_type = p_operation_type AND is_active = TRUE;



    IF operation_cost IS NULL THEN

        RETURN QUERY SELECT FALSE, 0, 'Invalid operation type';

        RETURN;

    END IF;



    -- Get current credits with row lock to prevent race conditions

    SELECT credits_remaining INTO current_credits

    FROM users

    WHERE id = p_user_id

    FOR UPDATE;



    -- Check if user exists

    IF current_credits IS NULL THEN

        RETURN QUERY SELECT FALSE, 0, 'User not found';

        RETURN;

    END IF;



    -- Check if sufficient credits

    IF current_credits < p_credits_to_deduct THEN

        RETURN QUERY SELECT FALSE, current_credits, 'Insufficient credits';

        RETURN;

    END IF;



    -- Deduct credits atomically

    UPDATE users

    SET credits_remaining = credits_remaining - p_credits_to_deduct,

        updated_at = NOW()

    WHERE id = p_user_id;



    -- Set description

    final_description := COALESCE(p_description, 'AI generation: ' || p_operation_type);



    -- Log transaction with full audit trail

    INSERT INTO credit_transactions (

        user_id, credits_used, operation_type, description,

        metadata, study_set_id

    ) VALUES (

        p_user_id, p_credits_to_deduct, p_operation_type,

        final_description, p_metadata, p_study_set_id

    );



    -- Return success with new balance

    RETURN QUERY SELECT TRUE, (current_credits - p_credits_to_deduct), 'Credits deducted successfully';

END;

$$ LANGUAGE plpgsql SECURITY DEFINER;



-- Credit Addition for Purchases/Subscriptions

CREATE OR REPLACE FUNCTION add_credits(

    p_user_id UUID,

    p_credits_to_add INTEGER,

    p_source TEXT,

    p_reference_id TEXT DEFAULT NULL

)

RETURNS TABLE(success BOOLEAN, new_balance INTEGER, message TEXT) AS $$

DECLARE

    new_credits INTEGER;

BEGIN

    -- Validate inputs

    IF p_credits_to_add <= 0 THEN

        RETURN QUERY SELECT FALSE, 0, 'Invalid credit amount';

        RETURN;

    END IF;



    -- Add credits atomically

    UPDATE users

    SET credits_remaining = credits_remaining + p_credits_to_add,

        updated_at = NOW()

    WHERE id = p_user_id

    RETURNING credits_remaining INTO new_credits;



    -- Log transaction (negative credits_used indicates addition)

    INSERT INTO credit_transactions (

        user_id, credits_used, operation_type, description, metadata

    ) VALUES (

        p_user_id, -p_credits_to_add, 'credit_addition',

        'Credits added: ' || p_source,

        jsonb_build_object('source', p_source, 'reference_id', p_reference_id)

    );



    RETURN QUERY SELECT TRUE, new_credits, 'Credits added successfully';

END;

$$ LANGUAGE plpgsql SECURITY DEFINER;

```



### Security Middleware Stack



```typescript

// Complete security middleware setup

app.use(helmet()); // Security headers

app.use(cors({

  origin: process.env.FRONTEND_URL,

  credentials: true,

  optionsSuccessStatus: 200

}));



// Rate limiting

app.use('/api', generalLimit);

app.use('/api/ai', aiGenerationLimit);



// Body parsing with limits

app.use(express.json({ limit: '10mb' }));

app.use(express.urlencoded({ extended: true, limit: '10mb' }));



// Security headers

app.use((req, res, next) => {

  res.setHeader('X-Content-Type-Options', 'nosniff');

  res.setHeader('X-Frame-Options', 'DENY');

  res.setHeader('X-XSS-Protection', '1; mode=block');

  next();

});



// Audit logging

app.use('/api', auditLogger);

```



### Environment & Configuration Security



#### Backend Environment Variables

```bash

# All sensitive credentials stay server-side

SUPABASE_URL=your_supabase_project_url

SUPABASE_ANON_KEY=your_supabase_anon_key

SUPABASE_SERVICE_KEY=your_supabase_service_role_key  # CRITICAL: Never expose

STRIPE_SECRET_KEY=your_stripe_secret_key            # CRITICAL: Never expose

STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret    # CRITICAL: Never expose

OPENROUTER_API_KEY=your_openrouter_api_key         # CRITICAL: Never expose

JWT_SECRET=your_jwt_secret_key                     # CRITICAL: Never expose

BCRYPT_SALT_ROUNDS=12

PORT=5000

NODE_ENV=production

```



#### Frontend Environment Variables

```bash

# Frontend only knows about your backend

VITE_API_BASE_URL=https://your-domain.com/api

# NO OTHER CREDENTIALS - Everything goes through your backend

```



### Security Checklist



#### ✅ Authentication & Authorization

- [x] JWT tokens with proper expiration (1 hour)

- [x] bcrypt with salt rounds ≥ 12

- [x] Resource ownership verification on all operations

- [x] Supabase Row Level Security policies

- [x] No authentication credentials in frontend



#### ✅ Input Validation & Sanitization

- [x] Comprehensive input validation with express-validator

- [x] HTML sanitization with DOMPurify

- [x] File upload restrictions and validation

- [x] SQL injection prevention via parameterized queries



#### ✅ API Security

- [x] Rate limiting per user and IP

- [x] CORS properly configured

- [x] Security headers (helmet.js)

- [x] Request size limits

- [x] Error messages don't leak sensitive data



#### ✅ Business Logic Security

- [x] Atomic credit operations

- [x] Credit manipulation prevention

- [x] Audit logging for sensitive operations

- [x] External API call sanitization



#### ✅ Infrastructure Security

- [x] HTTPS enforcement

- [x] Environment variables properly secured

- [x] No sensitive data in frontend code

- [x] Proper secret management



#### ✅ Data Protection

- [x] Encryption in transit (HTTPS)

- [x] Encryption at rest (Supabase)

- [x] Personal data protection

- [x] Secure file storage (Supabase Storage)



### Key Security Principles



1. **Zero Trust Frontend**: Frontend never has access to external service credentials

2. **Layered Security**: Multiple validation layers at different levels

3. **Principle of Least Privilege**: Users only access their own resources

4. **Audit Everything**: Log all sensitive operations

5. **Fail Securely**: Default to deny access when in doubt

6. **Defense in Depth**: Multiple security controls for each threat



### Database Security Enhancements Summary



#### ✅ Row Level Security (RLS) Implementation

- **Complete data isolation** between users across all tables

- **Inheritance security** for related tables (flashcards/quiz questions inherit study_set ownership)

- **Append-only audit tables** prevent tampering with credit transaction history

- **Admin-only configuration** for AI operation costs and sensitive settings



#### ✅ Stored Procedure Security

- **Atomic credit operations** prevent race conditions and ensure data consistency

- **Input validation** at database level with proper error handling

- **Comprehensive audit logging** for all credit transactions with metadata

- **SECURITY DEFINER** functions ensure consistent permissions regardless of caller



#### ✅ Enhanced Data Integrity

- **Check constraints** prevent invalid data (file sizes, credit amounts, difficulty levels)

- **Foreign key cascades** maintain referential integrity across related tables

- **Automatic triggers** for item count updates and data consistency

- **Performance indexes** on frequently queried fields for optimal performance



#### ✅ Advanced Access Control

- **Granular permissions** per operation type with configurable costs

- **Resource ownership verification** at multiple levels

- **No direct credit manipulation** by users - all operations through stored procedures

- **Secure function execution** with proper error handling and logging



This comprehensive security implementation ensures that even if someone gains database access, they cannot:

- Access other users' data due to RLS policies

- Manipulate credits directly due to stored procedure enforcement

- Bypass audit logging due to append-only transaction tables

- Corrupt data integrity due to constraints and triggers



---



## 13. Third-Party Integration Guide



### Secure External API Integration



```typescript

class SecureAPIService {

  private openRouter: OpenRouter;

  private stripe: Stripe;



  constructor() {

    // All API keys server-side only - NEVER exposed to frontend

    this.openRouter = new OpenRouter({

      apiKey: process.env.OPENROUTER_API_KEY!

    });



    this.stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {

      apiVersion: '2023-10-16'

    });

  }



  // Sanitize data before sending to external APIs

  async generateContent(prompt: string, userId: string): Promise<any> {

    // Log for audit trail

    await this.logAPIUsage(userId, 'openrouter', 'generation');



    // Sanitize prompt to prevent injection

    const sanitizedPrompt = DOMPurify.sanitize(prompt);



    try {

      const response = await this.openRouter.chat.completions.create({

        model: 'google/gemini-2.5-pro',

        messages: [{ role: 'user', content: sanitizedPrompt }],

        max_tokens: 2000,

        temperature: 0.7

      });



      return response;

    } catch (error) {

      // Log errors without exposing internal details

      await this.logAPIError(userId, 'openrouter', error);

      throw new Error('Content generation failed');

    }

  }



  // Secure Stripe operations

  async createPaymentIntent(amount: number, customerId: string): Promise<any> {

    try {

      return await this.stripe.paymentIntents.create({

        amount,

        currency: 'usd',

        customer: customerId,

        // Add metadata for tracking

        metadata: {

          service: 'chewyai',

          type: 'credit_purchase'

        }

      });

    } catch (error) {

      await this.logAPIError(customerId, 'stripe', error);

      throw new Error('Payment processing failed');

    }

  }

}

```



### OpenRouter AI Integration

```typescript

class AIService {

  private openrouter: OpenRouter;



  constructor() {

    this.openrouter = new OpenRouter({

      apiKey: process.env.OPENROUTER_API_KEY,

      defaultModel: 'google/gemini-2.5-pro'

    });

  }



  async generateFlashcards(content: string, customPrompt?: string): Promise<Flashcard[]> {

    const prompt = customPrompt

      ? `${customPrompt}\n\nGenerate flashcards from: ${content}`

      : `Generate study flashcards from the following content: ${content}`;



    const response = await this.openrouter.chat.completions.create({

      model: 'google/gemini-2.5-pro',

      messages: [{ role: 'user', content: prompt }],

      max_tokens: 2000

    });



    return this.parseFlashcardResponse(response.choices[0].message.content);

  }

}

```



### Stripe Integration

```typescript

class StripeService {

  private stripe: Stripe;



  async createSubscription(customerId: string, priceId: string) {

    return await this.stripe.subscriptions.create({

      customer: customerId,

      items: [{ price: priceId }],

      payment_behavior: 'default_incomplete',

      expand: ['latest_invoice.payment_intent']

    });

  }



  async purchaseCredits(customerId: string, creditPackage: string) {

    const prices = {

      '25_credits': 'price_25_credits_id',

      '50_credits': 'price_50_credits_id',

      '100_credits': 'price_100_credits_id'

    };



    return await this.stripe.paymentIntents.create({

      amount: this.getCreditPackagePrice(creditPackage),

      currency: 'usd',

      customer: customerId,

      metadata: { credit_package: creditPackage }

    });

  }

}

```



### Document Processing

```typescript

class DocumentProcessor {

  async processPDF(buffer: Buffer): Promise<string> {

    const data = await pdfParse(buffer);

    return data.text;

  }



  async processDocx(buffer: Buffer): Promise<string> {

    const result = await mammoth.extractRawText({ buffer });

    return result.value;

  }



  async processPPTX(buffer: Buffer): Promise<string> {

    // Custom PPTX processing implementation

    return extractTextFromPPTX(buffer);

  }

}

```



---



## 14. Scalability and Performance



### Database Optimization

- **Connection Pooling:** Supabase handles connection management

- **Query Optimization:** Proper indexing on frequently queried fields

- **Read Replicas:** Utilize Supabase read replicas for heavy read operations



### Caching Strategy

- **API Response Caching:** Redis for frequently accessed data

- **Static Asset Caching:** CDN for frontend assets

- **Database Query Caching:** Supabase query caching



### Performance Monitoring

- **API Metrics:** Response times, error rates

- **Credit Usage Tracking:** Monitor AI generation costs

- **User Behavior Analytics:** Study patterns and feature usage



### Scaling Considerations

- **Horizontal Scaling:** Multiple backend instances behind load balancer

- **File Storage:** Move to cloud storage (S3/Supabase Storage) for documents

- **AI Service Limits:** Implement queuing for high-volume AI requests



---



## Development Timeline



### Phase 1: Foundation (Weeks 1-2)

- [ ] Project setup and monorepo structure

- [ ] Supabase database schema and authentication

- [ ] Basic frontend components with dark theme

- [ ] Backend API structure with TypeScript



### Phase 2: Core Features (Weeks 3-4)

- [ ] Document upload and processing

- [ ] Basic study set creation (manual)

- [ ] Flashcard interface with keyboard navigation

- [ ] Credit system implementation



### Phase 3: AI Integration (Weeks 5-6)

- [ ] OpenRouter API integration

- [ ] Multi-document selection UI

- [ ] AI generation workflows

- [ ] Custom prompt functionality



### Phase 4: Advanced Features (Weeks 7-8)

- [ ] Quiz interface with all question types

- [ ] Study set editing and management

- [ ] Embedded document viewer

- [ ] AI generation from document viewer



### Phase 5: Business Logic (Weeks 9-10)

- [ ] Stripe subscription integration

- [ ] Credit purchasing system

- [ ] Subscription tier enforcement

- [ ] User dashboard and analytics



### Phase 6: Polish & Production (Weeks 11-12)

- [ ] UI/UX refinements

- [ ] Performance optimization

- [ ] Production deployment setup

- [ ] Testing and bug fixes



---



This comprehensive PRD provides all the necessary information for one-shot development of ChewyAI, with clear specifications, flexible architecture, and detailed implementation guidance.