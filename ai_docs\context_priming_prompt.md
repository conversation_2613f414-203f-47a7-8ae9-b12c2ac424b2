# ChewyAI Project Context Priming Prompt

## Instructions for AI Assistant
Use this prompt to quickly understand the ChewyAI project state and continue development work seamlessly.

## Step 1: Read Core Project Documentation
**REQUIRED**: Read these files to understand the project scope and requirements:

1. **`ChewyAI_Complete_PRD_v3.md`** - Complete Product Requirements Document
   - Contains comprehensive technical specifications
   - Section 2.5 has critical shared TypeScript types
   - Database schema, API specifications, and component requirements

2. **`ChewyAI_Functional_Spec.md`** - Functional Requirements
   - Business model and user workflows
   - Security and compliance requirements
   - Performance specifications

## Step 2: Review Implementation Progress
**REQUIRED**: Review all files in the `chewyai_spec/` folder to understand current implementation state:

### Completed Task Files:
- `01_foundation_setup.md` - Project structure and shared types
- `02_database_schema.md` - Supabase database with RLS policies
- `03_authentication_system.md` - Backend auth with Supabase
- `04_frontend_auth_components.md` - React auth components with Zustand
- `05_document_management_backend.md` - File upload and processing
- `06_credit_system_backend.md` - Credit management with atomic operations
- `07_ai_integration_backend.md` - OpenRouter AI with Gemini model
- `08_study_set_management.md` - Study set CRUD and progress tracking
- `09_frontend_document_management.md` - Document upload UI components
- `10_ai_generation_frontend.md` - AI generation forms and workflows

### Check for Additional Files:
Look for any additional numbered files (11+) that may have been created since this prompt was written.

## Step 3: Understand Technical Stack
**Key Technologies:**
- **Frontend**: React 18 + TypeScript + TailwindCSS + Vite + Zustand + React Query
- **Backend**: Express.js + TypeScript + Supabase (PostgreSQL + Auth) + Stripe
- **AI**: OpenRouter API with Gemini-2.0-flash-exp model
- **Architecture**: Monorepo with frontend building into backend's public folder
- **Design**: Dark space theme (#0a0a0a) with purple accents (#8b5cf6, #a855f7)

## Step 4: Assess Current State
**Determine where to continue by:**

1. **Check the last completed task file number** in `chewyai_spec/`
2. **Identify the next logical phase** based on the sequence:
   - Foundation → Database → Auth → Document Management → Credits → AI → Frontend Components
3. **Look for any incomplete implementations** or follow-up work needed

## Step 5: Continue Development
**Expected remaining phases** (if not already completed):
- Frontend study interfaces (flashcard/quiz components)
- Credit management frontend
- Payment integration with Stripe
- Dashboard and analytics
- Testing and deployment

## Development Standards
**Maintain these standards established in previous work:**

### Code Quality:
- Use shared TypeScript types from the PRD
- Implement proper error handling and validation
- Follow security best practices (RLS policies, input validation)
- Use atomic operations for credit management
- Implement responsive design patterns

### File Organization:
- Each phase should have detailed task files with:
  - Complete code implementations
  - File structure specifications
  - Acceptance criteria
  - Dependency mappings
  - Estimated time requirements

### Technical Approach:
- Use package managers for dependency management
- Implement Row Level Security for data isolation
- Use stored procedures for atomic database operations
- Follow the established component patterns and naming conventions
- Maintain the dark theme design system

## Step 6: Git Workflow Setup
**REQUIRED**: Establish proper Git workflow before making any changes:

### 6.1 Check Current Git State
```bash
# Check current branch and status
git status
git branch -a
git log --oneline -5
```

### 6.2 Create Feature Branch
**Always work in feature branches, never directly on main or staging:**

```bash
# Create and switch to feature branch for current phase
git checkout -b feature/phase-[NUMBER]-[DESCRIPTION]
# Example: git checkout -b feature/phase-11-study-interfaces
```

**Branch Naming Convention:**
- `feature/phase-[NUMBER]-[DESCRIPTION]` - For new phase implementations
- `feature/fix-[DESCRIPTION]` - For bug fixes
- `feature/enhance-[DESCRIPTION]` - For enhancements

### 6.3 Verify Branch Protection
Check if staging and main branches have protection rules:
```bash
# Use GitHub API to check branch protection
# This ensures we follow proper PR workflow
```

## Step 7: Implementation Workflow
**Follow this systematic approach for robust development:**

### 7.1 Plan Before Code
1. **Create/Update PROGRESS.md** with current phase details
2. **Review task file** for the current phase thoroughly
3. **Identify all files** that need to be created/modified
4. **Plan commit strategy** - logical, atomic commits

### 7.2 Implement with Git Discipline
```bash
# Make frequent, logical commits during implementation
git add [specific-files]
git commit -m "feat(phase-X): implement [specific-component]

- Add [specific functionality]
- Include [specific features]
- Follow [specific patterns]

Refs: #[issue-number] if applicable"
```

**Commit Message Format:**
- `feat(phase-X): description` - New features
- `fix(phase-X): description` - Bug fixes
- `docs(phase-X): description` - Documentation
- `test(phase-X): description` - Tests
- `refactor(phase-X): description` - Code refactoring

### 7.3 Testing and Validation
Before creating PR:
```bash
# Test implementations if applicable
npm test
npm run build
npm run lint

# Check for any issues
git status
git diff --staged
```

## Step 8: Pull Request Workflow
**Create systematic PRs for code review and integration:**

### 8.1 Push Feature Branch
```bash
# Push feature branch to remote
git push origin feature/phase-[NUMBER]-[DESCRIPTION]
```

### 8.2 Create Pull Request to Staging
**Use GitHub API or web interface:**
- **Target**: `staging` branch (never directly to main)
- **Title**: `Phase [NUMBER]: [Description]`
- **Description**: Include:
  - Summary of changes
  - Files created/modified
  - Testing performed
  - Acceptance criteria met
  - Any dependencies or notes

### 8.3 PR Review Process
- **Self-review**: Check all changes before requesting review
- **Automated checks**: Ensure CI/CD passes
- **Manual testing**: Verify functionality works as expected

## Step 9: Branch Management
**Maintain clean branch structure:**

### 9.1 Staging Branch
- **Purpose**: Integration testing and validation
- **Merges from**: Feature branches
- **Merges to**: Main branch (after validation)
- **Protection**: Require PR reviews, passing tests

### 9.2 Main Branch
- **Purpose**: Production-ready code
- **Merges from**: Staging branch only
- **Protection**: Require PR reviews, admin approval
- **Tags**: Version releases

### 9.3 Cleanup
```bash
# After successful merge to staging
git checkout staging
git pull origin staging
git branch -d feature/phase-[NUMBER]-[DESCRIPTION]
git push origin --delete feature/phase-[NUMBER]-[DESCRIPTION]
```

## Step 10: Identify Next Actions
**After reviewing all documentation and setting up Git workflow:**

1. **Determine the current implementation phase**
2. **Create appropriate feature branch**
3. **Update PROGRESS.md with current work**
4. **Identify any gaps or incomplete work**
5. **Continue with the next logical task file creation**
6. **Maintain the established patterns and quality standards**
7. **Follow Git workflow for all changes**

## Important Notes
- **Always read the actual files** - don't assume content based on filenames
- **Check for any PROGRESS.md** files that might indicate ongoing work
- **Verify Supabase and Stripe MCP servers are available** for testing
- **Follow the established task file format** for consistency
- **Each task file should be self-contained** with complete implementations
- **Never commit directly to main or staging** - always use feature branches
- **Create atomic, well-documented commits**
- **Use GitHub API tools** for branch management and PR creation

## Context Verification Checklist
Before continuing development, confirm you understand:
- [ ] Project scope and business model from PRD
- [ ] Current implementation phase from task files
- [ ] Technical stack and architecture decisions
- [ ] Established coding patterns and standards
- [ ] Next logical development steps
- [ ] Available MCP servers (Supabase, Stripe) for testing
- [ ] Git workflow and branch structure
- [ ] PR process and review requirements

## Ready to Continue
Once you've completed the context priming steps above, you should be fully prepared to continue ChewyAI development from exactly where the previous session left off, maintaining consistency, quality standards, and proper Git workflow practices.
