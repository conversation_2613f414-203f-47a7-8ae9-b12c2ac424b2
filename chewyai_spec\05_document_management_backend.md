# Phase 5: Document Management Backend
**Priority**: HIGH - Core feature for content processing
**Dependencies**: Phase 1 (Foundation), Phase 2 (Database), Phase 3 (Authentication)
**Estimated Time**: 4-5 hours

## Overview
Implement complete document upload, processing, and storage system with support for PDF, DOCX, TXT, and PPTX files.

## Tasks

### 5.1 Document Processing Service
**File**: `backend/src/services/documentService.ts`

```typescript
import pdf from 'pdf-parse';
import mammoth from 'mammoth';
import { supabase } from './supabaseService';

export class DocumentProcessor {
  async processFile(file: Express.Multer.File): Promise<string> {
    switch (file.mimetype) {
      case 'application/pdf':
        return this.processPDF(file.buffer);
      case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
        return this.processDocx(file.buffer);
      case 'text/plain':
        return this.processText(file.buffer);
      case 'application/vnd.openxmlformats-officedocument.presentationml.presentation':
        return this.processPPTX(file.buffer);
      default:
        throw new Error('Unsupported file type');
    }
  }

  private async processPDF(buffer: Buffer): Promise<string> {
    try {
      const data = await pdf(buffer);
      return this.cleanText(data.text);
    } catch (error) {
      throw new Error(`PDF processing failed: ${error.message}`);
    }
  }

  private async processDocx(buffer: Buffer): Promise<string> {
    try {
      const result = await mammoth.extractRawText({ buffer });
      return this.cleanText(result.value);
    } catch (error) {
      throw new Error(`DOCX processing failed: ${error.message}`);
    }
  }

  private async processText(buffer: Buffer): Promise<string> {
    try {
      const text = buffer.toString('utf-8');
      return this.cleanText(text);
    } catch (error) {
      throw new Error(`Text processing failed: ${error.message}`);
    }
  }

  private async processPPTX(buffer: Buffer): Promise<string> {
    // For PPTX, we'll use a simple text extraction
    // In production, consider using a library like 'officegen' or 'node-pptx'
    try {
      // Basic PPTX text extraction (simplified)
      const text = buffer.toString('utf-8');
      // Extract readable text from PPTX XML structure
      const textMatches = text.match(/<a:t[^>]*>([^<]+)<\/a:t>/g);
      if (textMatches) {
        const extractedText = textMatches
          .map(match => match.replace(/<[^>]+>/g, ''))
          .join(' ');
        return this.cleanText(extractedText);
      }
      return 'Unable to extract text from PowerPoint file';
    } catch (error) {
      throw new Error(`PPTX processing failed: ${error.message}`);
    }
  }

  private cleanText(text: string): string {
    return text
      .replace(/\s+/g, ' ') // Replace multiple whitespace with single space
      .replace(/\n\s*\n/g, '\n') // Remove empty lines
      .trim();
  }
}

// File upload service for Supabase Storage
export class FileUploadService {
  async uploadFile(fileName: string, fileBuffer: Buffer, mimeType: string): Promise<string> {
    try {
      const { data, error } = await supabase.storage
        .from('documents')
        .upload(fileName, fileBuffer, {
          contentType: mimeType,
          upsert: false
        });

      if (error) {
        throw new Error(`File upload failed: ${error.message}`);
      }

      return data.path;
    } catch (error) {
      throw new Error(`Storage upload failed: ${error.message}`);
    }
  }

  async deleteFile(filePath: string): Promise<void> {
    try {
      const { error } = await supabase.storage
        .from('documents')
        .remove([filePath]);

      if (error) {
        throw new Error(`File deletion failed: ${error.message}`);
      }
    } catch (error) {
      console.error('File deletion error:', error);
      // Don't throw here as this is cleanup
    }
  }

  async getFileUrl(filePath: string): Promise<string> {
    try {
      const { data } = await supabase.storage
        .from('documents')
        .createSignedUrl(filePath, 3600); // 1 hour expiry

      if (!data?.signedUrl) {
        throw new Error('Failed to generate file URL');
      }

      return data.signedUrl;
    } catch (error) {
      throw new Error(`URL generation failed: ${error.message}`);
    }
  }
}
```

### 5.2 Document Database Service
**File**: `backend/src/services/documentDbService.ts`

```typescript
import { supabase } from './supabaseService';
import { DocumentMetadata, DocumentWithContent, DocumentFileType } from '../../../shared/types';

export class DocumentDbService {
  async createDocument(documentData: {
    user_id: string;
    filename: string;
    file_type: DocumentFileType;
    file_size: number;
    content_text: string;
    supabase_storage_path: string;
    is_processed: boolean;
    processing_error?: string;
  }): Promise<DocumentMetadata> {
    const { data, error } = await supabase
      .from('documents')
      .insert(documentData)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to create document: ${error.message}`);
    }

    return data;
  }

  async getUserDocuments(userId: string, limit = 50, offset = 0): Promise<DocumentMetadata[]> {
    const { data, error } = await supabase
      .from('documents')
      .select('id, user_id, filename, file_type, file_size, supabase_storage_path, uploaded_at, is_processed, processing_error')
      .eq('user_id', userId)
      .order('uploaded_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      throw new Error(`Failed to get documents: ${error.message}`);
    }

    return data || [];
  }

  async getDocumentById(documentId: string, userId: string): Promise<DocumentWithContent | null> {
    const { data, error } = await supabase
      .from('documents')
      .select('*')
      .eq('id', documentId)
      .eq('user_id', userId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null; // Not found
      throw new Error(`Failed to get document: ${error.message}`);
    }

    return data;
  }

  async getDocumentsByIds(documentIds: string[], userId: string): Promise<DocumentWithContent[]> {
    const { data, error } = await supabase
      .from('documents')
      .select('*')
      .in('id', documentIds)
      .eq('user_id', userId);

    if (error) {
      throw new Error(`Failed to get documents: ${error.message}`);
    }

    return data || [];
  }

  async updateDocument(documentId: string, userId: string, updates: Partial<DocumentMetadata>): Promise<DocumentMetadata> {
    const { data, error } = await supabase
      .from('documents')
      .update(updates)
      .eq('id', documentId)
      .eq('user_id', userId)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to update document: ${error.message}`);
    }

    return data;
  }

  async deleteDocument(documentId: string, userId: string): Promise<void> {
    const { error } = await supabase
      .from('documents')
      .delete()
      .eq('id', documentId)
      .eq('user_id', userId);

    if (error) {
      throw new Error(`Failed to delete document: ${error.message}`);
    }
  }

  async searchDocuments(userId: string, query: string, limit = 20): Promise<DocumentMetadata[]> {
    const { data, error } = await supabase
      .from('documents')
      .select('id, user_id, filename, file_type, file_size, supabase_storage_path, uploaded_at, is_processed, processing_error')
      .eq('user_id', userId)
      .or(`filename.ilike.%${query}%,content_text.ilike.%${query}%`)
      .order('uploaded_at', { ascending: false })
      .limit(limit);

    if (error) {
      throw new Error(`Failed to search documents: ${error.message}`);
    }

    return data || [];
  }
}

export const documentDbService = new DocumentDbService();
```

### 5.3 Document Controller
**File**: `backend/src/controllers/documentController.ts`

```typescript
import { Request, Response } from 'express';
import multer from 'multer';
import { DocumentProcessor, FileUploadService } from '../services/documentService';
import { documentDbService } from '../services/documentDbService';
import { DocumentFileType } from '../../../shared/types';

// Configure multer for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = [
      'application/pdf',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation'
    ];
    
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Unsupported file type'), false);
    }
  }
});

const processor = new DocumentProcessor();
const fileUploadService = new FileUploadService();

export const uploadDocument = [
  upload.single('document'),
  async (req: Request, res: Response) => {
    try {
      if (!req.file) {
        return res.status(400).json({
          success: false,
          error: 'No file uploaded'
        });
      }

      const userId = req.user!.id;
      const file = req.file;

      // Process file and extract text
      let extractedText: string;
      try {
        extractedText = await processor.processFile(file);
      } catch (processingError) {
        console.error('File processing error:', processingError);
        return res.status(415).json({
          success: false,
          error: 'Unsupported file type or corrupted file'
        });
      }

      // Upload original file to Supabase Storage
      const fileName = `${userId}/${Date.now()}_${file.originalname}`;
      let storagePath: string;
      
      try {
        storagePath = await fileUploadService.uploadFile(fileName, file.buffer, file.mimetype);
      } catch (storageError) {
        console.error('Storage upload error:', storageError);
        return res.status(500).json({
          success: false,
          error: 'Failed to store file'
        });
      }

      // Insert document metadata and content into database
      try {
        const documentData = {
          user_id: userId,
          filename: file.originalname,
          file_type: getFileTypeFromMimetype(file.mimetype),
          file_size: file.size,
          content_text: extractedText,
          supabase_storage_path: storagePath,
          is_processed: true,
          processing_error: null
        };

        const document = await documentDbService.createDocument(documentData);

        return res.status(201).json({
          success: true,
          data: document,
          message: 'Document uploaded and processed successfully'
        });

      } catch (dbError) {
        console.error('Database error:', dbError);
        
        // Cleanup - delete uploaded file if database insert fails
        try {
          await fileUploadService.deleteFile(storagePath);
        } catch (cleanupError) {
          console.error('Failed to cleanup file after database error:', cleanupError);
        }

        return res.status(500).json({
          success: false,
          error: 'Failed to save document metadata'
        });
      }

    } catch (error) {
      console.error('Unexpected error in uploadDocument:', error);
      return res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }
];

export const getDocuments = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;
    const limit = parseInt(req.query.limit as string) || 50;
    const offset = parseInt(req.query.offset as string) || 0;

    const documents = await documentDbService.getUserDocuments(userId, limit, offset);

    res.json({
      success: true,
      data: documents
    });
  } catch (error) {
    console.error('Get documents error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve documents'
    });
  }
};

export const getDocument = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;
    const documentId = req.params.id;

    const document = await documentDbService.getDocumentById(documentId, userId);

    if (!document) {
      return res.status(404).json({
        success: false,
        error: 'Document not found'
      });
    }

    res.json({
      success: true,
      data: document
    });
  } catch (error) {
    console.error('Get document error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve document'
    });
  }
};

export const deleteDocument = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;
    const documentId = req.params.id;

    // Get document to find storage path
    const document = await documentDbService.getDocumentById(documentId, userId);
    
    if (!document) {
      return res.status(404).json({
        success: false,
        error: 'Document not found'
      });
    }

    // Delete from database first
    await documentDbService.deleteDocument(documentId, userId);

    // Delete from storage (don't fail if this fails)
    try {
      await fileUploadService.deleteFile(document.supabase_storage_path);
    } catch (storageError) {
      console.error('Storage deletion error:', storageError);
      // Continue - database deletion succeeded
    }

    res.json({
      success: true,
      message: 'Document deleted successfully'
    });
  } catch (error) {
    console.error('Delete document error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete document'
    });
  }
};

export const searchDocuments = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;
    const query = req.query.q as string;

    if (!query || query.trim().length < 2) {
      return res.status(400).json({
        success: false,
        error: 'Search query must be at least 2 characters'
      });
    }

    const documents = await documentDbService.searchDocuments(userId, query.trim());

    res.json({
      success: true,
      data: documents
    });
  } catch (error) {
    console.error('Search documents error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to search documents'
    });
  }
};

function getFileTypeFromMimetype(mimetype: string): DocumentFileType {
  const typeMap = {
    'application/pdf': 'pdf' as const,
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'docx' as const,
    'text/plain': 'txt' as const,
    'application/vnd.openxmlformats-officedocument.presentationml.presentation': 'pptx' as const
  };
  
  return typeMap[mimetype] || 'txt';
}
```

### 5.4 Document Routes
**File**: `backend/src/routes/documents.ts`

```typescript
import { Router } from 'express';
import { authenticateToken } from '../middleware/auth';
import {
  uploadDocument,
  getDocuments,
  getDocument,
  deleteDocument,
  searchDocuments
} from '../controllers/documentController';

const router = Router();

// All document routes require authentication
router.use(authenticateToken);

// Document CRUD operations
router.post('/upload', uploadDocument);
router.get('/', getDocuments);
router.get('/search', searchDocuments);
router.get('/:id', getDocument);
router.delete('/:id', deleteDocument);

export default router;
```

## Acceptance Criteria
- [ ] File upload supports PDF, DOCX, TXT, and PPTX formats
- [ ] Text extraction works correctly for all supported formats
- [ ] Files are stored securely in Supabase Storage
- [ ] Document metadata is saved to database with proper relationships
- [ ] File size limits (10MB) are enforced
- [ ] Error handling for corrupted or unsupported files
- [ ] Document search functionality works across filename and content
- [ ] Document deletion removes both database records and storage files
- [ ] All operations respect user ownership (RLS policies)
- [ ] Proper cleanup on failed operations

## Next Phase Dependencies
- Phase 6 (Document Management Frontend) requires these API endpoints
- Phase 7 (AI Integration) requires document content retrieval
- Phase 8 (Study Set Creation) requires document selection functionality
