# Phase 1: Foundation Setup
**Priority**: CRITICAL - Must be completed first
**Dependencies**: None
**Estimated Time**: 2-3 hours

## Overview
Establish the core project structure, shared types, and foundational configurations that all other components depend on.

## Tasks

### 1.1 Project Structure & Configuration
**Files to Create:**
```
chewy-ai/
├── package.json (root workspace)
├── frontend/
│   ├── package.json
│   ├── tsconfig.json
│   ├── vite.config.ts
│   ├── tailwind.config.js
│   ├── index.html
│   └── src/
├── backend/
│   ├── package.json
│   ├── tsconfig.json
│   └── src/
├── shared/
│   └── types.ts
└── README.md
```

**Root package.json:**
```json
{
  "name": "chewy-ai",
  "private": true,
  "workspaces": ["frontend", "backend"],
  "scripts": {
    "dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"",
    "dev:frontend": "cd frontend && npm run dev",
    "dev:backend": "cd backend && npm run dev",
    "build": "npm run build:frontend && npm run build:backend",
    "build:frontend": "cd frontend && npm run build",
    "build:backend": "cd backend && npm run build"
  },
  "devDependencies": {
    "concurrently": "^8.2.2"
  }
}
```

### 1.2 Shared TypeScript Types
**File**: `shared/types.ts`
**Critical**: This is the single source of truth for all data models

**Complete type definitions** (copy exactly from PRD section 2.5):
- UserProfile interface
- AuthResult interface  
- DocumentMetadata and DocumentWithContent interfaces
- StudySet, Flashcard, QuizQuestion interfaces
- CreditTransaction and AIOperationCost interfaces
- APIResponse and PaginatedResponse interfaces
- Component prop interfaces (ButtonProps, InputProps, ModalProps)

### 1.3 Frontend Configuration
**Frontend package.json dependencies:**
```json
{
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "react-router-dom": "^6.8.1",
    "zustand": "^4.3.6",
    "@tanstack/react-query": "^4.28.0",
    "framer-motion": "^10.12.4",
    "react-icons": "^4.8.0"
  },
  "devDependencies": {
    "@types/react": "^18.0.28",
    "@types/react-dom": "^18.0.11",
    "@vitejs/plugin-react": "^3.1.0",
    "typescript": "^4.9.3",
    "vite": "^4.2.0",
    "tailwindcss": "^3.3.0",
    "autoprefixer": "^10.4.14",
    "postcss": "^8.4.21"
  }
}
```

**Vite config** with proxy to backend:
```typescript
export default defineConfig({
  plugins: [react()],
  server: {
    proxy: {
      '/api': 'http://localhost:3001'
    }
  },
  build: {
    outDir: '../backend/public'
  }
})
```

**Tailwind config** with dark theme and purple accents:
```javascript
module.exports = {
  content: ['./src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      colors: {
        primary: {
          500: '#8b5cf6',
          600: '#7c3aed'
        },
        background: {
          primary: '#0a0a0a',
          secondary: '#1a1a1a'
        }
      }
    }
  }
}
```

### 1.4 Backend Configuration  
**Backend package.json dependencies:**
```json
{
  "dependencies": {
    "express": "^4.18.2",
    "cors": "^2.8.5",
    "helmet": "^6.1.5",
    "bcrypt": "^5.1.0",
    "multer": "^1.4.5-lts.1",
    "pdf-parse": "^1.1.1",
    "mammoth": "^1.5.1",
    "@supabase/supabase-js": "^2.21.0",
    "stripe": "^12.0.0",
    "dotenv": "^16.0.3"
  },
  "devDependencies": {
    "@types/express": "^4.17.17",
    "@types/cors": "^2.8.13",
    "@types/bcrypt": "^5.0.0",
    "@types/multer": "^1.4.7",
    "typescript": "^4.9.3",
    "ts-node": "^10.9.1",
    "nodemon": "^2.0.22"
  }
}
```

## Acceptance Criteria
- [ ] All package.json files created with correct dependencies
- [ ] TypeScript configurations working for both frontend and backend
- [ ] Shared types file created with all interfaces from PRD
- [ ] Vite builds frontend into backend/public directory
- [ ] Tailwind CSS configured with dark theme
- [ ] Development scripts work: `npm run dev` starts both frontend and backend
- [ ] No TypeScript compilation errors
- [ ] Project structure matches specification exactly

## Next Phase Dependencies
- Phase 2 (Database Setup) requires shared types to be complete
- Phase 3 (Authentication) requires backend configuration
- All subsequent phases depend on this foundation
