# Phase 6: Credit System Backend
**Priority**: HIGH - Required for AI generation features
**Dependencies**: Phase 1 (Foundation), Phase 2 (Database), Phase 3 (Authentication)
**Estimated Time**: 3-4 hours

## Overview
Implement the complete credit management system with atomic operations, transaction logging, and dynamic pricing configuration.

## Tasks

### 6.1 Credit Service Implementation
**File**: `backend/src/services/creditService.ts`

```typescript
import { supabase } from './supabaseService';
import { CreditTransaction, AIOperationCost } from '../../../shared/types';

export class CreditService {
  // Get current AI operation cost
  async getOperationCost(operationType: string): Promise<number> {
    const { data, error } = await supabase
      .from('ai_operation_costs')
      .select('credits_required')
      .eq('operation_type', operationType)
      .eq('is_active', true)
      .single();

    if (error || !data) {
      throw new Error(`Invalid operation type: ${operationType}`);
    }

    return data.credits_required;
  }

  // Get all active operation costs
  async getAllOperationCosts(): Promise<AIOperationCost[]> {
    const { data, error } = await supabase
      .from('ai_operation_costs')
      .select('*')
      .eq('is_active', true)
      .order('operation_type');

    if (error) {
      throw new Error(`Failed to get operation costs: ${error.message}`);
    }

    return data || [];
  }

  // Atomic credit deduction using stored procedure
  async deductCredits(
    userId: string,
    operationType: string,
    metadata?: any,
    studySetId?: string
  ): Promise<{ success: boolean; remainingCredits: number; message: string }> {
    const cost = await this.getOperationCost(operationType);

    const { data, error } = await supabase.rpc('deduct_credits', {
      p_user_id: userId,
      p_credits_to_deduct: cost,
      p_operation_type: operationType,
      p_description: `AI generation: ${operationType}`,
      p_metadata: metadata || {},
      p_study_set_id: studySetId || null
    });

    if (error) {
      throw new CreditError('Credit deduction failed', error);
    }

    return data[0]; // Returns {success, remaining_credits, message}
  }

  // Add credits for purchases/subscriptions
  async addCredits(
    userId: string,
    creditsToAdd: number,
    source: string,
    referenceId?: string
  ): Promise<{ success: boolean; newBalance: number; message: string }> {
    const { data, error } = await supabase.rpc('add_credits', {
      p_user_id: userId,
      p_credits_to_add: creditsToAdd,
      p_source: source,
      p_reference_id: referenceId || null
    });

    if (error) {
      throw new CreditError('Credit addition failed', error);
    }

    return data[0];
  }

  // Get user's current credit balance
  async getUserCredits(userId: string): Promise<number> {
    const { data, error } = await supabase
      .from('users')
      .select('credits_remaining')
      .eq('id', userId)
      .single();

    if (error || !data) {
      throw new Error('User not found');
    }

    return data.credits_remaining;
  }

  // Validate credit operation before execution
  async validateCreditOperation(userId: string, operationType: string): Promise<boolean> {
    const [userCredits, requiredCredits] = await Promise.all([
      this.getUserCredits(userId),
      this.getOperationCost(operationType)
    ]);

    return userCredits >= requiredCredits;
  }

  // Get user's credit transaction history
  async getCreditHistory(
    userId: string,
    limit = 50,
    offset = 0
  ): Promise<{ transactions: CreditTransaction[]; total: number }> {
    // Get transactions
    const { data: transactions, error: transError } = await supabase
      .from('credit_transactions')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (transError) {
      throw new Error(`Failed to get credit history: ${transError.message}`);
    }

    // Get total count
    const { count, error: countError } = await supabase
      .from('credit_transactions')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId);

    if (countError) {
      throw new Error(`Failed to get credit history count: ${countError.message}`);
    }

    return {
      transactions: transactions || [],
      total: count || 0
    };
  }

  // Get credit usage statistics
  async getCreditStats(userId: string, days = 30): Promise<{
    totalUsed: number;
    totalAdded: number;
    operationBreakdown: { operation_type: string; credits_used: number; count: number }[];
  }> {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const { data, error } = await supabase
      .from('credit_transactions')
      .select('credits_used, operation_type')
      .eq('user_id', userId)
      .gte('created_at', startDate.toISOString());

    if (error) {
      throw new Error(`Failed to get credit stats: ${error.message}`);
    }

    const transactions = data || [];
    
    const totalUsed = transactions
      .filter(t => t.credits_used > 0)
      .reduce((sum, t) => sum + t.credits_used, 0);
    
    const totalAdded = Math.abs(transactions
      .filter(t => t.credits_used < 0)
      .reduce((sum, t) => sum + t.credits_used, 0));

    // Group by operation type
    const operationMap = new Map<string, { credits_used: number; count: number }>();
    
    transactions
      .filter(t => t.credits_used > 0)
      .forEach(t => {
        const existing = operationMap.get(t.operation_type) || { credits_used: 0, count: 0 };
        operationMap.set(t.operation_type, {
          credits_used: existing.credits_used + t.credits_used,
          count: existing.count + 1
        });
      });

    const operationBreakdown = Array.from(operationMap.entries()).map(([operation_type, stats]) => ({
      operation_type,
      ...stats
    }));

    return {
      totalUsed,
      totalAdded,
      operationBreakdown
    };
  }

  // Check if user has sufficient credits for operation
  async checkSufficientCredits(userId: string, operationType: string): Promise<{
    sufficient: boolean;
    currentCredits: number;
    requiredCredits: number;
    shortfall: number;
  }> {
    const [currentCredits, requiredCredits] = await Promise.all([
      this.getUserCredits(userId),
      this.getOperationCost(operationType)
    ]);

    const sufficient = currentCredits >= requiredCredits;
    const shortfall = sufficient ? 0 : requiredCredits - currentCredits;

    return {
      sufficient,
      currentCredits,
      requiredCredits,
      shortfall
    };
  }
}

// Custom error class for credit operations
export class CreditError extends Error {
  constructor(message: string, public originalError?: any) {
    super(message);
    this.name = 'CreditError';
  }
}

export const creditService = new CreditService();
```

### 6.2 Credit Controller
**File**: `backend/src/controllers/creditController.ts`

```typescript
import { Request, Response } from 'express';
import { creditService } from '../services/creditService';

export const getCreditBalance = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;
    const credits = await creditService.getUserCredits(userId);

    res.json({
      success: true,
      data: { credits }
    });
  } catch (error) {
    console.error('Get credit balance error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get credit balance'
    });
  }
};

export const getCreditHistory = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;
    const limit = parseInt(req.query.limit as string) || 50;
    const offset = parseInt(req.query.offset as string) || 0;

    const result = await creditService.getCreditHistory(userId, limit, offset);

    res.json({
      success: true,
      data: result.transactions,
      total: result.total,
      page: Math.floor(offset / limit) + 1,
      limit,
      hasMore: offset + limit < result.total
    });
  } catch (error) {
    console.error('Get credit history error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get credit history'
    });
  }
};

export const getCreditStats = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;
    const days = parseInt(req.query.days as string) || 30;

    const stats = await creditService.getCreditStats(userId, days);

    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('Get credit stats error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get credit statistics'
    });
  }
};

export const getOperationCosts = async (req: Request, res: Response) => {
  try {
    const costs = await creditService.getAllOperationCosts();

    res.json({
      success: true,
      data: costs
    });
  } catch (error) {
    console.error('Get operation costs error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get operation costs'
    });
  }
};

export const checkCreditSufficiency = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;
    const { operationType } = req.params;

    const result = await creditService.checkSufficientCredits(userId, operationType);

    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error('Check credit sufficiency error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to check credit sufficiency'
    });
  }
};
```

### 6.3 Credit Middleware
**File**: `backend/src/middleware/creditCheck.ts`

```typescript
import { Request, Response, NextFunction } from 'express';
import { creditService } from '../services/creditService';

// Middleware to check if user has sufficient credits for an operation
export const requireCredits = (operationType: string) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      const userId = req.user!.id;
      
      const hasCredits = await creditService.validateCreditOperation(userId, operationType);
      
      if (!hasCredits) {
        const creditCheck = await creditService.checkSufficientCredits(userId, operationType);
        
        return res.status(402).json({
          success: false,
          error: 'Insufficient credits',
          data: {
            currentCredits: creditCheck.currentCredits,
            requiredCredits: creditCheck.requiredCredits,
            shortfall: creditCheck.shortfall
          }
        });
      }

      // Store operation type in request for later use
      req.creditOperation = operationType;
      next();
    } catch (error) {
      console.error('Credit check error:', error);
      res.status(500).json({
        success: false,
        error: 'Credit validation failed'
      });
    }
  };
};

// Extend Express Request type
declare global {
  namespace Express {
    interface Request {
      creditOperation?: string;
    }
  }
}
```

### 6.4 Credit Routes
**File**: `backend/src/routes/credits.ts`

```typescript
import { Router } from 'express';
import { authenticateToken } from '../middleware/auth';
import {
  getCreditBalance,
  getCreditHistory,
  getCreditStats,
  getOperationCosts,
  checkCreditSufficiency
} from '../controllers/creditController';

const router = Router();

// All credit routes require authentication
router.use(authenticateToken);

// Credit information routes
router.get('/balance', getCreditBalance);
router.get('/history', getCreditHistory);
router.get('/stats', getCreditStats);
router.get('/pricing', getOperationCosts);
router.get('/check/:operationType', checkCreditSufficiency);

export default router;
```

### 6.5 Integration with Main App
**File**: `backend/src/app.ts` (additions)

```typescript
// Add to existing app.ts
import creditRoutes from './routes/credits';

// Add after other route registrations
app.use('/api/credits', creditRoutes);
```

## Acceptance Criteria
- [ ] Credit deduction uses atomic stored procedures
- [ ] Credit balance retrieval works correctly
- [ ] Credit history shows all transactions with proper pagination
- [ ] Operation costs are configurable through database
- [ ] Credit validation prevents operations when insufficient credits
- [ ] Credit statistics provide useful insights
- [ ] Error handling for all credit operations
- [ ] Middleware properly checks credit requirements
- [ ] All operations respect user ownership
- [ ] Transaction logging captures all necessary metadata

## Next Phase Dependencies
- Phase 7 (AI Integration) requires credit deduction functionality
- Phase 8 (Payment System) requires credit addition functionality
- Phase 9 (Frontend Credit Components) requires these API endpoints
- All AI-powered features depend on credit validation
