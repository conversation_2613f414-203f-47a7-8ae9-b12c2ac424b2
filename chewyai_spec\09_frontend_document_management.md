# Phase 9: Frontend Document Management
**Priority**: HIGH - Core user interface for document handling
**Dependencies**: Phase 1 (Foundation), Phase 4 (Auth Components), Phase 5 (Document Backend)
**Estimated Time**: 4-5 hours

## Overview
Create React components for document upload, management, and selection with drag-and-drop functionality and file processing feedback.

## Tasks

### 9.1 Document Store (Zustand)
**File**: `frontend/src/stores/documentStore.ts`

```typescript
import { create } from 'zustand';
import { DocumentMetadata, DocumentWithContent } from '../../../shared/types';

interface DocumentState {
  documents: DocumentMetadata[];
  selectedDocuments: Set<string>;
  isLoading: boolean;
  uploadProgress: { [key: string]: number };
  
  // Actions
  fetchDocuments: () => Promise<void>;
  uploadDocument: (file: File) => Promise<DocumentMetadata>;
  deleteDocument: (id: string) => Promise<void>;
  searchDocuments: (query: string) => Promise<DocumentMetadata[]>;
  getDocument: (id: string) => Promise<DocumentWithContent | null>;
  toggleDocumentSelection: (id: string) => void;
  clearSelection: () => void;
  selectAll: () => void;
  setUploadProgress: (fileName: string, progress: number) => void;
}

export const useDocumentStore = create<DocumentState>((set, get) => ({
  documents: [],
  selectedDocuments: new Set(),
  isLoading: false,
  uploadProgress: {},

  fetchDocuments: async () => {
    set({ isLoading: true });
    
    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch('/api/documents', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch documents');
      }

      const result = await response.json();
      
      if (result.success) {
        set({ documents: result.data, isLoading: false });
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error('Fetch documents error:', error);
      set({ isLoading: false });
      throw error;
    }
  },

  uploadDocument: async (file: File) => {
    const formData = new FormData();
    formData.append('document', file);

    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch('/api/documents/upload', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
        body: formData,
      });

      if (!response.ok) {
        const errorResult = await response.json();
        throw new Error(errorResult.error || 'Upload failed');
      }

      const result = await response.json();
      
      if (result.success) {
        // Add new document to the list
        set(state => ({
          documents: [result.data, ...state.documents],
          uploadProgress: { ...state.uploadProgress, [file.name]: 100 }
        }));
        
        return result.data;
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error('Upload document error:', error);
      throw error;
    }
  },

  deleteDocument: async (id: string) => {
    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch(`/api/documents/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        const errorResult = await response.json();
        throw new Error(errorResult.error || 'Delete failed');
      }

      // Remove document from the list
      set(state => ({
        documents: state.documents.filter(doc => doc.id !== id),
        selectedDocuments: new Set([...state.selectedDocuments].filter(docId => docId !== id))
      }));
    } catch (error) {
      console.error('Delete document error:', error);
      throw error;
    }
  },

  searchDocuments: async (query: string) => {
    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch(`/api/documents/search?q=${encodeURIComponent(query)}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Search failed');
      }

      const result = await response.json();
      return result.success ? result.data : [];
    } catch (error) {
      console.error('Search documents error:', error);
      return [];
    }
  },

  getDocument: async (id: string) => {
    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch(`/api/documents/${id}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        return null;
      }

      const result = await response.json();
      return result.success ? result.data : null;
    } catch (error) {
      console.error('Get document error:', error);
      return null;
    }
  },

  toggleDocumentSelection: (id: string) => {
    set(state => {
      const newSelection = new Set(state.selectedDocuments);
      if (newSelection.has(id)) {
        newSelection.delete(id);
      } else {
        newSelection.add(id);
      }
      return { selectedDocuments: newSelection };
    });
  },

  clearSelection: () => {
    set({ selectedDocuments: new Set() });
  },

  selectAll: () => {
    set(state => ({
      selectedDocuments: new Set(state.documents.map(doc => doc.id))
    }));
  },

  setUploadProgress: (fileName: string, progress: number) => {
    set(state => ({
      uploadProgress: { ...state.uploadProgress, [fileName]: progress }
    }));
  }
}));
```

### 9.2 Document Upload Component
**File**: `frontend/src/components/documents/DocumentUpload.tsx`

```typescript
import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { useDocumentStore } from '../../stores/documentStore';
import { Button } from '../common/Button';

export const DocumentUpload: React.FC = () => {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadErrors, setUploadErrors] = useState<string[]>([]);
  const { uploadDocument, setUploadProgress } = useDocumentStore();

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    setIsUploading(true);
    setUploadErrors([]);

    const errors: string[] = [];

    for (const file of acceptedFiles) {
      try {
        // Validate file size (10MB limit)
        if (file.size > 10 * 1024 * 1024) {
          errors.push(`${file.name}: File size exceeds 10MB limit`);
          continue;
        }

        // Validate file type
        const allowedTypes = [
          'application/pdf',
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          'text/plain',
          'application/vnd.openxmlformats-officedocument.presentationml.presentation'
        ];

        if (!allowedTypes.includes(file.type)) {
          errors.push(`${file.name}: Unsupported file type. Please upload PDF, DOCX, TXT, or PPTX files.`);
          continue;
        }

        // Set initial progress
        setUploadProgress(file.name, 0);

        // Upload file
        await uploadDocument(file);
        
        // Set completion progress
        setUploadProgress(file.name, 100);
      } catch (error) {
        errors.push(`${file.name}: ${error.message}`);
      }
    }

    setUploadErrors(errors);
    setIsUploading(false);
  }, [uploadDocument, setUploadProgress]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'text/plain': ['.txt'],
      'application/vnd.openxmlformats-officedocument.presentationml.presentation': ['.pptx']
    },
    multiple: true,
    disabled: isUploading
  });

  return (
    <div className="space-y-4">
      <div
        {...getRootProps()}
        className={`
          border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors
          ${isDragActive 
            ? 'border-primary-500 bg-primary-500/10' 
            : 'border-gray-600 hover:border-primary-500 hover:bg-primary-500/5'
          }
          ${isUploading ? 'opacity-50 cursor-not-allowed' : ''}
        `}
      >
        <input {...getInputProps()} />
        
        <div className="space-y-2">
          <svg
            className="mx-auto h-12 w-12 text-gray-400"
            stroke="currentColor"
            fill="none"
            viewBox="0 0 48 48"
          >
            <path
              d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
              strokeWidth={2}
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
          
          {isDragActive ? (
            <p className="text-primary-400">Drop the files here...</p>
          ) : (
            <div>
              <p className="text-gray-300">
                Drag & drop files here, or{' '}
                <span className="text-primary-500 font-medium">browse</span>
              </p>
              <p className="text-sm text-gray-500 mt-1">
                Supports PDF, DOCX, TXT, PPTX (max 10MB each)
              </p>
            </div>
          )}
        </div>
      </div>

      {isUploading && (
        <div className="bg-background-secondary rounded-lg p-4">
          <p className="text-sm text-gray-300 mb-2">Uploading files...</p>
          <div className="space-y-2">
            {/* Progress bars would be shown here */}
          </div>
        </div>
      )}

      {uploadErrors.length > 0 && (
        <div className="bg-red-900/20 border border-red-700 rounded-lg p-4">
          <h4 className="text-red-400 font-medium mb-2">Upload Errors:</h4>
          <ul className="text-sm text-red-300 space-y-1">
            {uploadErrors.map((error, index) => (
              <li key={index}>• {error}</li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};
```

### 9.3 Document List Component
**File**: `frontend/src/components/documents/DocumentList.tsx`

```typescript
import React, { useEffect, useState } from 'react';
import { useDocumentStore } from '../../stores/documentStore';
import { DocumentCard } from './DocumentCard';
import { Input } from '../common/Input';
import { Button } from '../common/Button';

export const DocumentList: React.FC = () => {
  const {
    documents,
    selectedDocuments,
    isLoading,
    fetchDocuments,
    searchDocuments,
    clearSelection,
    selectAll,
    deleteDocument
  } = useDocumentStore();

  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState(null);
  const [isSearching, setIsSearching] = useState(false);

  useEffect(() => {
    fetchDocuments();
  }, [fetchDocuments]);

  const handleSearch = async () => {
    if (searchQuery.trim().length < 2) {
      setSearchResults(null);
      return;
    }

    setIsSearching(true);
    try {
      const results = await searchDocuments(searchQuery.trim());
      setSearchResults(results);
    } catch (error) {
      console.error('Search error:', error);
    } finally {
      setIsSearching(false);
    }
  };

  const clearSearch = () => {
    setSearchQuery('');
    setSearchResults(null);
  };

  const handleBulkDelete = async () => {
    if (selectedDocuments.size === 0) return;

    const confirmDelete = window.confirm(
      `Are you sure you want to delete ${selectedDocuments.size} document(s)? This action cannot be undone.`
    );

    if (!confirmDelete) return;

    try {
      const deletePromises = Array.from(selectedDocuments).map(id => deleteDocument(id));
      await Promise.all(deletePromises);
      clearSelection();
    } catch (error) {
      console.error('Bulk delete error:', error);
      alert('Some documents could not be deleted. Please try again.');
    }
  };

  const displayDocuments = searchResults || documents;
  const hasSelection = selectedDocuments.size > 0;

  if (isLoading && documents.length === 0) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-gray-400">Loading documents...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Search and Actions */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <div className="flex gap-2">
            <Input
              placeholder="Search documents..."
              value={searchQuery}
              onChange={setSearchQuery}
              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
            />
            <Button
              onClick={handleSearch}
              isLoading={isSearching}
              disabled={searchQuery.trim().length < 2}
            >
              Search
            </Button>
            {searchResults && (
              <Button
                onClick={clearSearch}
                variant="secondary"
              >
                Clear
              </Button>
            )}
          </div>
        </div>

        {/* Bulk Actions */}
        {hasSelection && (
          <div className="flex gap-2">
            <Button
              onClick={selectAll}
              variant="secondary"
              size="sm"
            >
              Select All
            </Button>
            <Button
              onClick={clearSelection}
              variant="secondary"
              size="sm"
            >
              Clear ({selectedDocuments.size})
            </Button>
            <Button
              onClick={handleBulkDelete}
              variant="danger"
              size="sm"
            >
              Delete Selected
            </Button>
          </div>
        )}
      </div>

      {/* Search Results Info */}
      {searchResults && (
        <div className="text-sm text-gray-400">
          Found {searchResults.length} document(s) matching "{searchQuery}"
        </div>
      )}

      {/* Document Grid */}
      {displayDocuments.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">
            {searchResults ? 'No documents found matching your search.' : 'No documents uploaded yet.'}
          </div>
          {!searchResults && (
            <p className="text-sm text-gray-500">
              Upload your first document to get started with AI-powered study materials.
            </p>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {displayDocuments.map((document) => (
            <DocumentCard
              key={document.id}
              document={document}
            />
          ))}
        </div>
      )}
    </div>
  );
};
```

### 9.4 Document Card Component
**File**: `frontend/src/components/documents/DocumentCard.tsx`

```typescript
import React, { useState } from 'react';
import { DocumentMetadata } from '../../../../shared/types';
import { useDocumentStore } from '../../stores/documentStore';
import { Button } from '../common/Button';

interface DocumentCardProps {
  document: DocumentMetadata;
}

export const DocumentCard: React.FC<DocumentCardProps> = ({ document }) => {
  const { selectedDocuments, toggleDocumentSelection, deleteDocument } = useDocumentStore();
  const [isDeleting, setIsDeleting] = useState(false);

  const isSelected = selectedDocuments.has(document.id);

  const handleDelete = async () => {
    const confirmDelete = window.confirm(
      `Are you sure you want to delete "${document.filename}"? This action cannot be undone.`
    );

    if (!confirmDelete) return;

    setIsDeleting(true);
    try {
      await deleteDocument(document.id);
    } catch (error) {
      console.error('Delete error:', error);
      alert('Failed to delete document. Please try again.');
    } finally {
      setIsDeleting(false);
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getFileIcon = (fileType: string) => {
    const icons = {
      pdf: '📄',
      docx: '📝',
      txt: '📃',
      pptx: '📊'
    };
    return icons[fileType] || '📄';
  };

  return (
    <div
      className={`
        bg-background-secondary rounded-lg p-4 border-2 transition-all cursor-pointer
        ${isSelected 
          ? 'border-primary-500 bg-primary-500/10' 
          : 'border-gray-700 hover:border-gray-600'
        }
      `}
      onClick={() => toggleDocumentSelection(document.id)}
    >
      {/* Header */}
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center space-x-2 flex-1 min-w-0">
          <span className="text-2xl">{getFileIcon(document.file_type)}</span>
          <div className="min-w-0 flex-1">
            <h3 className="text-white font-medium truncate" title={document.filename}>
              {document.filename}
            </h3>
            <p className="text-sm text-gray-400">
              {document.file_type.toUpperCase()} • {formatFileSize(document.file_size)}
            </p>
          </div>
        </div>

        {/* Selection Indicator */}
        <div
          className={`
            w-5 h-5 rounded border-2 flex items-center justify-center
            ${isSelected 
              ? 'bg-primary-500 border-primary-500' 
              : 'border-gray-500'
            }
          `}
        >
          {isSelected && (
            <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
            </svg>
          )}
        </div>
      </div>

      {/* Processing Status */}
      {!document.is_processed && (
        <div className="mb-3">
          <div className="flex items-center space-x-2 text-yellow-400">
            <div className="w-2 h-2 bg-yellow-400 rounded-full animate-pulse"></div>
            <span className="text-sm">Processing...</span>
          </div>
        </div>
      )}

      {document.processing_error && (
        <div className="mb-3">
          <div className="text-red-400 text-sm">
            ⚠️ Processing failed: {document.processing_error}
          </div>
        </div>
      )}

      {/* Upload Date */}
      <div className="text-xs text-gray-500 mb-3">
        Uploaded {formatDate(document.uploaded_at)}
      </div>

      {/* Actions */}
      <div className="flex justify-end space-x-2" onClick={(e) => e.stopPropagation()}>
        <Button
          onClick={handleDelete}
          variant="danger"
          size="sm"
          isLoading={isDeleting}
        >
          Delete
        </Button>
      </div>
    </div>
  );
};
```

### 9.5 Document Management Page
**File**: `frontend/src/pages/DocumentsPage.tsx`

```typescript
import React from 'react';
import { DocumentUpload } from '../components/documents/DocumentUpload';
import { DocumentList } from '../components/documents/DocumentList';

export const DocumentsPage: React.FC = () => {
  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="space-y-8">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-bold text-white">Documents</h1>
          <p className="mt-2 text-gray-400">
            Upload and manage your study documents. Supported formats: PDF, DOCX, TXT, PPTX
          </p>
        </div>

        {/* Upload Section */}
        <div className="bg-background-secondary rounded-lg p-6">
          <h2 className="text-xl font-semibold text-white mb-4">Upload Documents</h2>
          <DocumentUpload />
        </div>

        {/* Document List */}
        <div>
          <h2 className="text-xl font-semibold text-white mb-4">Your Documents</h2>
          <DocumentList />
        </div>
      </div>
    </div>
  );
};
```

## Acceptance Criteria
- [ ] Drag-and-drop file upload with validation
- [ ] File type and size validation (PDF, DOCX, TXT, PPTX, 10MB max)
- [ ] Upload progress indication and error handling
- [ ] Document list with search functionality
- [ ] Document selection for bulk operations
- [ ] Document deletion with confirmation
- [ ] Processing status display for uploaded files
- [ ] Responsive design for mobile and desktop
- [ ] Error states and loading indicators
- [ ] File metadata display (size, type, upload date)

## Next Phase Dependencies
- Phase 10 (AI Generation Frontend) requires document selection functionality
- Phase 11 (Study Set Creation) requires document management integration
- Phase 12 (Dashboard) requires document statistics and recent uploads
